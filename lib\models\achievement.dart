import 'package:flutter/material.dart';

/// Achievement model for Metro Dash
class Achievement {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final int targetValue;
  final AchievementType type;
  bool isUnlocked;
  int currentProgress;
  final int coinReward;

  Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.targetValue,
    required this.type,
    this.isUnlocked = false,
    this.currentProgress = 0,
    this.coinReward = 50,
  });

  /// Get progress percentage (0.0 to 1.0)
  double get progressPercentage {
    if (targetValue == 0) return 1.0;
    return (currentProgress / targetValue).clamp(0.0, 1.0);
  }

  /// Check if achievement is completed
  bool get isCompleted => currentProgress >= targetValue;

  /// Update progress and check if unlocked
  bool updateProgress(int value) {
    if (isUnlocked) return false;
    
    currentProgress = value;
    if (isCompleted && !isUnlocked) {
      isUnlocked = true;
      return true; // Achievement just unlocked
    }
    return false;
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'isUnlocked': isUnlocked,
      'currentProgress': currentProgress,
    };
  }

  /// Create from JSON
  factory Achievement.fromJson(Map<String, dynamic> json, Achievement template) {
    return Achievement(
      id: template.id,
      name: template.name,
      description: template.description,
      icon: template.icon,
      color: template.color,
      targetValue: template.targetValue,
      type: template.type,
      isUnlocked: json['isUnlocked'] ?? false,
      currentProgress: json['currentProgress'] ?? 0,
      coinReward: template.coinReward,
    );
  }

  /// Copy with new values
  Achievement copyWith({
    bool? isUnlocked,
    int? currentProgress,
  }) {
    return Achievement(
      id: id,
      name: name,
      description: description,
      icon: icon,
      color: color,
      targetValue: targetValue,
      type: type,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      currentProgress: currentProgress ?? this.currentProgress,
      coinReward: coinReward,
    );
  }
}

/// Types of achievements
enum AchievementType {
  score,
  distance,
  coins,
  games,
  powerUps,
  characters,
  special,
}

/// Achievement manager
class AchievementManager {
  static final List<Achievement> _allAchievements = [
    // Score achievements
    Achievement(
      id: 'score_1000',
      name: 'Getting Started',
      description: 'Score 1,000 points',
      icon: Icons.star,
      color: Colors.yellow,
      targetValue: 1000,
      type: AchievementType.score,
      coinReward: 50,
    ),
    Achievement(
      id: 'score_10000',
      name: 'Rising Star',
      description: 'Score 10,000 points',
      icon: Icons.star,
      color: Colors.orange,
      targetValue: 10000,
      type: AchievementType.score,
      coinReward: 100,
    ),
    Achievement(
      id: 'score_50000',
      name: 'Champion',
      description: 'Score 50,000 points',
      icon: Icons.emoji_events,
      color: Colors.gold,
      targetValue: 50000,
      type: AchievementType.score,
      coinReward: 250,
    ),

    // Distance achievements
    Achievement(
      id: 'distance_1000',
      name: 'First Steps',
      description: 'Run 1,000 meters',
      icon: Icons.directions_run,
      color: Colors.green,
      targetValue: 1000,
      type: AchievementType.distance,
      coinReward: 50,
    ),
    Achievement(
      id: 'distance_5000',
      name: 'Marathon Runner',
      description: 'Run 5,000 meters',
      icon: Icons.directions_run,
      color: Colors.blue,
      targetValue: 5000,
      type: AchievementType.distance,
      coinReward: 150,
    ),
    Achievement(
      id: 'distance_10000',
      name: 'Ultra Runner',
      description: 'Run 10,000 meters',
      icon: Icons.directions_run,
      color: Colors.purple,
      targetValue: 10000,
      type: AchievementType.distance,
      coinReward: 300,
    ),

    // Coin achievements
    Achievement(
      id: 'coins_100',
      name: 'Coin Collector',
      description: 'Collect 100 coins',
      icon: Icons.monetization_on,
      color: Colors.amber,
      targetValue: 100,
      type: AchievementType.coins,
      coinReward: 25,
    ),
    Achievement(
      id: 'coins_1000',
      name: 'Treasure Hunter',
      description: 'Collect 1,000 coins',
      icon: Icons.monetization_on,
      color: Colors.orange,
      targetValue: 1000,
      type: AchievementType.coins,
      coinReward: 100,
    ),
    Achievement(
      id: 'coins_5000',
      name: 'Gold Rush',
      description: 'Collect 5,000 coins',
      icon: Icons.monetization_on,
      color: Colors.deepOrange,
      targetValue: 5000,
      type: AchievementType.coins,
      coinReward: 500,
    ),

    // Games played achievements
    Achievement(
      id: 'games_10',
      name: 'Dedicated Player',
      description: 'Play 10 games',
      icon: Icons.play_arrow,
      color: Colors.cyan,
      targetValue: 10,
      type: AchievementType.games,
      coinReward: 75,
    ),
    Achievement(
      id: 'games_50',
      name: 'Veteran',
      description: 'Play 50 games',
      icon: Icons.play_arrow,
      color: Colors.indigo,
      targetValue: 50,
      type: AchievementType.games,
      coinReward: 200,
    ),
    Achievement(
      id: 'games_100',
      name: 'Master Player',
      description: 'Play 100 games',
      icon: Icons.play_arrow,
      color: Colors.deepPurple,
      targetValue: 100,
      type: AchievementType.games,
      coinReward: 500,
    ),

    // Power-up achievements
    Achievement(
      id: 'powerups_all',
      name: 'Power User',
      description: 'Use all power-up types',
      icon: Icons.flash_on,
      color: Colors.red,
      targetValue: 4, // 4 different power-up types
      type: AchievementType.powerUps,
      coinReward: 150,
    ),

    // Character achievements
    Achievement(
      id: 'characters_all',
      name: 'Collector',
      description: 'Unlock all characters',
      icon: Icons.people,
      color: Colors.teal,
      targetValue: 5, // 5 total characters
      type: AchievementType.characters,
      coinReward: 300,
    ),

    // Special achievements
    Achievement(
      id: 'first_run',
      name: 'Welcome!',
      description: 'Complete your first run',
      icon: Icons.celebration,
      color: Colors.pink,
      targetValue: 1,
      type: AchievementType.special,
      coinReward: 25,
    ),
    Achievement(
      id: 'perfect_run',
      name: 'Perfect Run',
      description: 'Complete a run without hitting obstacles',
      icon: Icons.star_border,
      color: Colors.gold,
      targetValue: 1,
      type: AchievementType.special,
      coinReward: 200,
    ),
  ];

  /// Get all achievements
  static List<Achievement> getAllAchievements() {
    return List.from(_allAchievements);
  }

  /// Get achievement by ID
  static Achievement? getAchievementById(String id) {
    try {
      return _allAchievements.firstWhere((achievement) => achievement.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get achievements by type
  static List<Achievement> getAchievementsByType(AchievementType type) {
    return _allAchievements.where((achievement) => achievement.type == type).toList();
  }

  /// Get unlocked achievements
  static List<Achievement> getUnlockedAchievements(List<Achievement> achievements) {
    return achievements.where((achievement) => achievement.isUnlocked).toList();
  }

  /// Get locked achievements
  static List<Achievement> getLockedAchievements(List<Achievement> achievements) {
    return achievements.where((achievement) => !achievement.isUnlocked).toList();
  }

  /// Calculate total coin rewards for unlocked achievements
  static int getTotalCoinRewards(List<Achievement> achievements) {
    return getUnlockedAchievements(achievements)
        .fold(0, (total, achievement) => total + achievement.coinReward);
  }

  /// Get completion percentage
  static double getCompletionPercentage(List<Achievement> achievements) {
    if (achievements.isEmpty) return 0.0;
    final unlockedCount = getUnlockedAchievements(achievements).length;
    return unlockedCount / achievements.length;
  }
}
