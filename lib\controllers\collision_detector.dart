import 'package:flutter/material.dart';
import 'package:flame/components.dart';
import '../game.dart';
import '../components/player.dart';
import '../components/obstacle.dart';
import '../components/coin.dart';
import '../components/powerup.dart';
import '../utils/helpers.dart';

/// Collision detection system for Metro Dash
class CollisionDetector {
  final MetroDashGame game;

  CollisionDetector(this.game);

  /// Check all collisions in the game
  void checkCollisions() {
    final player = game.player;

    // Check magnet effect on coins
    checkMagnetEffect(player);

    // Check obstacle collisions
    _checkObstacleCollisions(player);

    // Check coin collisions
    _checkCoinCollisions(player);

    // Check power-up collisions
    _checkPowerUpCollisions(player);
  }

  /// Check collisions between player and obstacles
  void _checkObstacleCollisions(Player player) {
    final playerRect = player.collisionRect;

    for (final obstacle in List.from(game.obstacles)) {
      if (!obstacle.isActive) continue;

      final obstacleRect = obstacle.collisionRect;

      if (_checkRectCollision(playerRect, obstacleRect)) {
        _handleObstacleCollision(player, obstacle);
        break; // Only handle one collision per frame
      }
    }
  }

  /// Check collisions between player and coins
  void _checkCoinCollisions(Player player) {
    final playerRect = player.collisionRect;

    for (final coin in List.from(game.coins)) {
      if (!coin.isActive || coin.isCollected) continue;

      final coinRect = coin.collisionRect;

      if (_checkRectCollision(playerRect, coinRect)) {
        _handleCoinCollision(player, coin);
      }
    }
  }

  /// Check collisions between player and power-ups
  void _checkPowerUpCollisions(Player player) {
    final playerRect = player.collisionRect;

    for (final powerUp in List.from(game.powerUps)) {
      if (!powerUp.isActive || powerUp.isCollected) continue;

      final powerUpRect = powerUp.collisionRect;

      if (_checkRectCollision(playerRect, powerUpRect)) {
        _handlePowerUpCollision(player, powerUp);
      }
    }
  }

  /// Check if two rectangles collide
  bool _checkRectCollision(Rect rect1, Rect rect2) {
    return GameHelpers.checkCollision(
      rect1.left,
      rect1.top,
      rect1.width,
      rect1.height,
      rect2.left,
      rect2.top,
      rect2.width,
      rect2.height,
    );
  }

  /// Handle collision between player and obstacle
  void _handleObstacleCollision(Player player, Obstacle obstacle) {
    // Check if player has shield
    if (player.isShielded) {
      // Shield absorbs the hit
      _handleShieldHit(player, obstacle);
      return;
    }

    // Check if player is sliding under obstacle
    if (player.isSliding && _canSlideUnder(obstacle)) {
      return; // No collision when sliding under
    }

    // Check if player is jumping over obstacle
    if (player.isJumping && _canJumpOver(obstacle)) {
      return; // No collision when jumping over
    }

    // Player hit obstacle - game over
    _handlePlayerHit(obstacle);
  }

  /// Handle collision between player and coin
  void _handleCoinCollision(Player player, Coin coin) {
    // Collect the coin
    coin.collect();

    // Update game statistics
    game.collectCoin(coin);

    // Create visual effect
    coin.createValueText();

    print('Coin collected! Value: ${coin.value}');
  }

  /// Handle collision between player and power-up
  void _handlePowerUpCollision(Player player, PowerUp powerUp) {
    // Collect the power-up
    powerUp.collect();

    // Apply power-up effect
    game.collectPowerUp(powerUp);

    // Create visual effect
    powerUp.createNameText();

    print('Power-up collected: ${powerUp.powerUpType.name}');
  }

  /// Handle shield hit
  void _handleShieldHit(Player player, Obstacle obstacle) {
    // Remove obstacle
    obstacle.isActive = false;
    obstacle.removeFromParent();
    game.obstacles.remove(obstacle);

    // Deactivate shield
    // The shield will be automatically deactivated by the player component

    // Visual feedback
    GameHelpers.lightVibrate();

    print('Shield absorbed obstacle hit!');
  }

  /// Handle player hit by obstacle
  void _handlePlayerHit(Obstacle obstacle) {
    // End the game
    game.hitObstacle(obstacle);

    print('Player hit obstacle: ${obstacle.obstacleType.name}');
  }

  /// Check if player can slide under obstacle
  bool _canSlideUnder(Obstacle obstacle) {
    // Some obstacles can be slid under (like barriers)
    switch (obstacle.obstacleType) {
      case ObstacleType.barrier:
        return true;
      case ObstacleType.sign:
        return true;
      case ObstacleType.train:
        return false; // Too big to slide under
      case ObstacleType.box:
        return true;
    }
  }

  /// Check if player can jump over obstacle
  bool _canJumpOver(Obstacle obstacle) {
    // Some obstacles can be jumped over
    switch (obstacle.obstacleType) {
      case ObstacleType.barrier:
        return true;
      case ObstacleType.sign:
        return true;
      case ObstacleType.train:
        return false; // Too tall to jump over
      case ObstacleType.box:
        return true;
    }
  }

  /// Check magnet effect on coins
  void checkMagnetEffect(Player player) {
    if (!game.gameController.magnetActive) return;

    final playerCenter = player.centerPosition;
    const magnetRange = 80.0;

    for (final coin in game.coins) {
      if (!coin.isActive || coin.isCollected) continue;

      final coinCenter = coin.centerPosition;
      final distance = (coinCenter - playerCenter).length;

      if (distance < magnetRange) {
        _attractCoinToPlayer(coin, player);
      }
    }
  }

  /// Attract coin to player (magnet effect)
  void _attractCoinToPlayer(Coin coin, Player player) {
    final coinCenter = coin.centerPosition;
    final playerCenter = player.centerPosition;
    final direction = (playerCenter - coinCenter).normalized();

    // Move coin towards player
    const attractionSpeed = 200.0;
    coin.position += direction * attractionSpeed * 0.016; // Assuming 60 FPS
  }

  /// Check jetpack collision avoidance
  bool checkJetpackAvoidance(Player player) {
    if (!game.gameController.jetpackActive) return false;

    // When jetpack is active, player flies over all obstacles
    return player.isJumping || player.position.y < player.groundY - 50;
  }

  /// Get collision info for debugging
  Map<String, dynamic> getCollisionInfo() {
    return {
      'obstacles': game.obstacles.length,
      'coins': game.coins.length,
      'powerUps': game.powerUps.length,
      'playerPosition': game.player.position,
      'playerRect': game.player.collisionRect,
    };
  }

  /// Check if position is safe (no obstacles)
  bool isPositionSafe(Vector2 position, Vector2 size) {
    final testRect = Rect.fromLTWH(position.x, position.y, size.x, size.y);

    for (final obstacle in game.obstacles) {
      if (!obstacle.isActive) continue;

      if (_checkRectCollision(testRect, obstacle.collisionRect)) {
        return false;
      }
    }

    return true;
  }

  /// Get nearest obstacle in player's track
  Obstacle? getNearestObstacle(Player player) {
    Obstacle? nearest;
    double nearestDistance = double.infinity;

    for (final obstacle in game.obstacles) {
      if (!obstacle.isActive) continue;

      // Check if obstacle is in same track
      if (obstacle.trackPosition == player.currentTrack) {
        final distance = (obstacle.position - player.position).length;
        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearest = obstacle;
        }
      }
    }

    return nearest;
  }

  /// Get collision prediction for AI or assistance
  bool predictCollision(Player player, double timeAhead) {
    final futurePlayerPos = Vector2(
      player.position.x,
      player.position.y + player.jumpVelocity * timeAhead,
    );

    for (final obstacle in game.obstacles) {
      if (!obstacle.isActive) continue;

      final futureObstaclePos = Vector2(
        obstacle.position.x,
        obstacle.position.y + obstacle.moveSpeed * timeAhead,
      );

      final futurePlayerRect = Rect.fromLTWH(
        futurePlayerPos.x,
        futurePlayerPos.y,
        player.size.x,
        player.size.y,
      );

      final futureObstacleRect = Rect.fromLTWH(
        futureObstaclePos.x,
        futureObstaclePos.y,
        obstacle.size.x,
        obstacle.size.y,
      );

      if (_checkRectCollision(futurePlayerRect, futureObstacleRect)) {
        return true;
      }
    }

    return false;
  }
}
