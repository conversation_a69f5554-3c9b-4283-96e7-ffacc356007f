import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'services/storage_service.dart';
import 'services/audio_service.dart';
import 'screens/home_screen.dart';
import 'utils/constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await StorageService.initialize();
  await AudioService.instance.initialize();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: GameConstants.backgroundColor,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  runApp(const MetroDashApp());
}

class MetroDashApp extends StatelessWidget {
  const MetroDashApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: GameConstants.gameTitle,
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: GameConstants.primaryColor,
        scaffoldBackgroundColor: GameConstants.backgroundColor,
        fontFamily: 'Roboto',
        textTheme: const TextTheme(
          displayLarge: TextStyle(color: GameConstants.textColor),
          displayMedium: TextStyle(color: GameConstants.textColor),
          displaySmall: TextStyle(color: GameConstants.textColor),
          headlineLarge: TextStyle(color: GameConstants.textColor),
          headlineMedium: TextStyle(color: GameConstants.textColor),
          headlineSmall: TextStyle(color: GameConstants.textColor),
          titleLarge: TextStyle(color: GameConstants.textColor),
          titleMedium: TextStyle(color: GameConstants.textColor),
          titleSmall: TextStyle(color: GameConstants.textColor),
          bodyLarge: TextStyle(color: GameConstants.textColor),
          bodyMedium: TextStyle(color: GameConstants.textColor),
          bodySmall: TextStyle(color: GameConstants.textColor),
          labelLarge: TextStyle(color: GameConstants.textColor),
          labelMedium: TextStyle(color: GameConstants.textColor),
          labelSmall: TextStyle(color: GameConstants.textColor),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: GameConstants.primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor: GameConstants.textColor,
            side: const BorderSide(color: GameConstants.primaryColor),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
          ),
        ),
      ),
      home: const HomeScreen(),
    );
  }
}
