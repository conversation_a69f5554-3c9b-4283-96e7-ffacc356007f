# Image Assets for Metro Dash

This directory contains all image files used in the Metro Dash game.

## Required Image Files:

### Backgrounds:
- `background.png` - Main game background
- `ground.png` - Ground/track texture
- `sky.png` - Sky background

### Characters:
- `player_default.png` - Default player character
- `player_speedster.png` - Speedster character
- `player_jumper.png` - High jumper character
- `player_slider.png` - Slider character
- `player_lucky.png` - Lucky character

### Obstacles:
- `barrier.png` - Barrier obstacle
- `train.png` - Train obstacle
- `sign.png` - Sign obstacle
- `box.png` - Box obstacle

### Collectibles:
- `coin_gold.png` - Gold coin
- `coin_silver.png` - Silver coin
- `coin_diamond.png` - Diamond coin

### Power-ups:
- `powerup_magnet.png` - Magnet power-up
- `powerup_shield.png` - Shield power-up
- `powerup_multiplier.png` - Score multiplier power-up
- `powerup_jetpack.png` - Jetpack power-up

### UI Elements:
- `button_play.png` - Play button
- `button_pause.png` - Pause button
- `button_settings.png` - Settings button
- `icon_coin.png` - Coin icon for UI
- `icon_star.png` - Star icon for score

## Image Specifications:
- Format: PNG with transparency support
- Resolution: 
  - Characters: 128x128 pixels
  - Obstacles: 128x128 to 256x256 pixels
  - Coins: 64x64 pixels
  - Power-ups: 96x96 pixels
  - UI elements: 64x64 to 128x128 pixels
  - Backgrounds: 1920x1080 or higher

## Usage:
These image files are loaded by the Flame game engine and displayed as sprites.
The game currently uses colored rectangles as placeholders.

## Art Style:
- Colorful and vibrant
- Cartoon/stylized look
- Clear and recognizable silhouettes
- Consistent art style across all assets

## Note:
The current game uses programmatically generated colored shapes.
Replace with actual artwork for the final game.
