import 'package:flutter/material.dart';

import '../models/player_data.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../services/audio_service.dart';
import '../services/storage_service.dart';

/// Game over screen with results and options
class GameOverScreen extends StatefulWidget {
  final int score;
  final int coins;
  final double distance;
  final PlayerData playerData;
  final VoidCallback onRestart;
  final VoidCallback onHome;

  const GameOverScreen({
    super.key,
    required this.score,
    required this.coins,
    required this.distance,
    required this.playerData,
    required this.onRestart,
    required this.onHome,
  });

  @override
  State<GameOverScreen> createState() => _GameOverScreenState();
}

class _GameOverScreenState extends State<GameOverScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  
  bool isNewHighScore = false;
  bool hasUpdatedData = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _checkHighScore();
    _updatePlayerData();
  }

  /// Setup animations
  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Start animations
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _scaleController.forward();
    });
  }

  /// Check if new high score
  void _checkHighScore() {
    isNewHighScore = widget.score > widget.playerData.highScore;
    if (isNewHighScore) {
      // Play celebration sound
      AudioService.instance.playPowerUpSound();
    }
  }

  /// Update player data
  Future<void> _updatePlayerData() async {
    if (hasUpdatedData) return;
    
    try {
      // Update player data
      final updatedData = widget.playerData.copyWith();
      updatedData.updateHighScore(widget.score);
      updatedData.addCoins(widget.coins);
      updatedData.recordGameCompletion(
        widget.score,
        widget.distance,
        const Duration(minutes: 1), // Placeholder duration
      );
      
      // Save to storage
      await StorageService.instance.savePlayerData(updatedData);
      hasUpdatedData = true;
    } catch (e) {
      print('Error updating player data: $e');
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black.withValues(alpha: 0.9),
      child: Center(
        child: SlideTransition(
          position: _slideAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              margin: const EdgeInsets.all(32),
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isNewHighScore 
                      ? GameConstants.secondaryColor
                      : GameConstants.primaryColor.withValues(alpha: 0.3),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: (isNewHighScore 
                        ? GameConstants.secondaryColor 
                        : GameConstants.primaryColor).withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildStats(),
                  const SizedBox(height: 32),
                  _buildButtons(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build header
  Widget _buildHeader() {
    return Column(
      children: [
        // Game Over title
        Text(
          'GAME OVER',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: isNewHighScore 
                ? GameConstants.secondaryColor 
                : GameConstants.textColor,
            shadows: const [
              Shadow(
                offset: Offset(2, 2),
                blurRadius: 4,
                color: Colors.black54,
              ),
            ],
          ),
        ),
        
        // New high score indicator
        if (isNewHighScore) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: GameConstants.secondaryColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: GameConstants.secondaryColor,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.star,
                  color: GameConstants.secondaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'NEW HIGH SCORE!',
                  style: TextStyle(
                    color: GameConstants.secondaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Build stats
  Widget _buildStats() {
    return Column(
      children: [
        // Score
        _buildStatRow(
          'Final Score',
          GameHelpers.formatScore(widget.score),
          Icons.star,
          isNewHighScore ? GameConstants.secondaryColor : GameConstants.primaryColor,
        ),
        
        const SizedBox(height: 16),
        
        // Distance and coins row
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Distance',
                '${(widget.distance / 10).floor()}m',
                Icons.straighten,
                GameConstants.primaryColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Coins',
                '${widget.coins}',
                Icons.monetization_on,
                GameConstants.coinColor,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Additional stats
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'High Score',
                GameHelpers.formatScore(
                  isNewHighScore ? widget.score : widget.playerData.highScore,
                ),
                Icons.emoji_events,
                GameConstants.secondaryColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'Total Coins',
                '${widget.playerData.totalCoins + widget.coins}',
                Icons.account_balance_wallet,
                GameConstants.coinColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build stat row
  Widget _buildStatRow(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Text(
                label,
                style: TextStyle(
                  color: GameConstants.textColor.withValues(alpha: 0.8),
                  fontSize: 16,
                ),
              ),
            ],
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Build stat card
  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: GameConstants.textColor.withValues(alpha: 0.7),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// Build buttons
  Widget _buildButtons() {
    return Column(
      children: [
        // Play again button
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton.icon(
            onPressed: () {
              AudioService.instance.playButtonClickSound();
              GameHelpers.lightVibrate();
              widget.onRestart();
            },
            icon: const Icon(Icons.refresh, color: Colors.white),
            label: const Text(
              'PLAY AGAIN',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: GameConstants.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Home button
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton.icon(
            onPressed: () {
              AudioService.instance.playButtonClickSound();
              GameHelpers.lightVibrate();
              widget.onHome();
            },
            icon: Icon(
              Icons.home,
              color: GameConstants.textColor.withValues(alpha: 0.8),
            ),
            label: Text(
              'HOME',
              style: TextStyle(
                color: GameConstants.textColor.withValues(alpha: 0.8),
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: GameConstants.textColor.withValues(alpha: 0.3),
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
