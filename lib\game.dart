import 'package:flame/game.dart';
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flutter/material.dart';

import 'utils/constants.dart';
import 'utils/helpers.dart';
import 'components/player.dart';
import 'components/obstacle.dart';
import 'components/coin.dart';
import 'components/powerup.dart';
import 'controllers/game_controller.dart';
import 'controllers/collision_detector.dart';
import 'controllers/input_handler.dart';
import 'services/audio_service.dart';

/// Main game class for Metro Dash
class MetroDashGame extends FlameGame
    with HasKeyboardHandlerComponents, HasCollisionDetection {
  late GameController gameController;
  late CollisionDetector collisionDetector;
  late InputHandler inputHandler;
  late Player player;
  late CameraComponent gameCamera;

  // Game state
  String gameState = GameConstants.gameStateMenu;
  double gameSpeed = GameConstants.gameSpeed;
  double distanceTraveled = 0.0;
  int score = 0;
  int coinsCollected = 0;
  int scoreMultiplier = 1;

  // Timers
  late Timer obstacleSpawnTimer;
  late Timer coinSpawnTimer;
  late Timer powerUpSpawnTimer;
  late Timer gameSpeedTimer;

  // Components lists
  final List<Obstacle> obstacles = [];
  final List<Coin> coins = [];
  final List<PowerUp> powerUps = [];

  // Background
  late SpriteComponent background;
  late SpriteComponent ground;

  // UI Components
  late TextComponent scoreText;
  late TextComponent distanceText;
  late TextComponent coinText;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Initialize game components
    await _initializeGame();
    await _setupCamera();
    await _setupBackground();
    await _setupUI();
    await _setupTimers();

    // Initialize controllers
    gameController = GameController(this);
    collisionDetector = CollisionDetector(this);
    inputHandler = InputHandler();

    // Create player
    player = Player();
    await add(player);

    // Add input handler
    await add(inputHandler);

    print('Metro Dash Game loaded successfully!');
  }

  /// Initialize game settings
  Future<void> _initializeGame() async {
    // Initialize audio
    await AudioService.instance.initialize();
  }

  /// Setup camera
  Future<void> _setupCamera() async {
    gameCamera = CameraComponent.withFixedResolution(
      width: GameConstants.gameWidth,
      height: GameConstants.gameHeight,
    );
    gameCamera.viewfinder.visibleGameSize = size;
    await add(gameCamera);
  }

  /// Setup background and ground
  Future<void> _setupBackground() async {
    // Create background (placeholder colored rectangle)
    background = SpriteComponent(
      size: Vector2(GameConstants.gameWidth, GameConstants.gameHeight),
      position: Vector2.zero(),
    );
    background.paint = Paint()..color = const Color(0xFF87CEEB); // Sky blue
    await add(background);

    // Create ground
    ground = SpriteComponent(
      size: Vector2(GameConstants.gameWidth, 100),
      position: Vector2(0, GameConstants.gameHeight - 100),
    );
    ground.paint = Paint()..color = const Color(0xFF8B4513); // Brown ground
    await add(ground);

    // Add track lines
    await _addTrackLines();
  }

  /// Add track separation lines
  Future<void> _addTrackLines() async {
    final trackWidth = GameConstants.trackWidth;

    // Left track line
    final leftLine = RectangleComponent(
      size: Vector2(2, GameConstants.gameHeight),
      position: Vector2(trackWidth, 0),
      paint: Paint()..color = Colors.white,
    );
    await add(leftLine);

    // Right track line
    final rightLine = RectangleComponent(
      size: Vector2(2, GameConstants.gameHeight),
      position: Vector2(trackWidth * 2, 0),
      paint: Paint()..color = Colors.white,
    );
    await add(rightLine);
  }

  /// Setup UI components
  Future<void> _setupUI() async {
    // Score text
    scoreText = TextComponent(
      text: 'Score: 0',
      position: Vector2(20, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    await add(scoreText);

    // Distance text
    distanceText = TextComponent(
      text: 'Distance: 0m',
      position: Vector2(20, 80),
      textRenderer: TextPaint(
        style: const TextStyle(color: Colors.white, fontSize: 18),
      ),
    );
    await add(distanceText);

    // Coins text
    coinText = TextComponent(
      text: 'Coins: 0',
      position: Vector2(GameConstants.gameWidth - 120, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: GameConstants.coinColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    await add(coinText);
  }

  /// Setup game timers
  Future<void> _setupTimers() async {
    // Obstacle spawn timer
    obstacleSpawnTimer = Timer(
      GameConstants.obstacleSpawnRate,
      onTick: _spawnObstacle,
      repeat: true,
    );

    // Coin spawn timer
    coinSpawnTimer = Timer(
      1.5, // Spawn coins every 1.5 seconds
      onTick: _spawnCoin,
      repeat: true,
    );

    // Power-up spawn timer
    powerUpSpawnTimer = Timer(
      8.0, // Spawn power-ups every 8 seconds
      onTick: _spawnPowerUp,
      repeat: true,
    );

    // Game speed increase timer
    gameSpeedTimer = Timer(
      5.0, // Increase speed every 5 seconds
      onTick: _increaseGameSpeed,
      repeat: true,
    );
  }

  /// Start the game
  void startGame() {
    gameState = GameConstants.gameStatePlaying;
    _resetGameStats();

    // Start timers
    obstacleSpawnTimer.start();
    coinSpawnTimer.start();
    powerUpSpawnTimer.start();
    gameSpeedTimer.start();

    // Start background music
    AudioService.instance.playBackgroundMusic();

    print('Game started!');
  }

  /// Pause the game
  void pauseGame() {
    if (gameState == GameConstants.gameStatePlaying) {
      gameState = GameConstants.gameStatePaused;

      // Pause timers
      obstacleSpawnTimer.stop();
      coinSpawnTimer.stop();
      powerUpSpawnTimer.stop();
      gameSpeedTimer.stop();

      // Pause music
      AudioService.instance.pauseBackgroundMusic();
    }
  }

  /// Resume the game
  void resumeGame() {
    if (gameState == GameConstants.gameStatePaused) {
      gameState = GameConstants.gameStatePlaying;

      // Resume timers
      obstacleSpawnTimer.start();
      coinSpawnTimer.start();
      powerUpSpawnTimer.start();
      gameSpeedTimer.start();

      // Resume music
      AudioService.instance.resumeBackgroundMusic();
    }
  }

  /// End the game
  void endGame() {
    gameState = GameConstants.gameStateGameOver;

    // Stop timers
    obstacleSpawnTimer.stop();
    coinSpawnTimer.stop();
    powerUpSpawnTimer.stop();
    gameSpeedTimer.stop();

    // Stop music
    AudioService.instance.stopBackgroundMusic();

    // Play crash sound
    AudioService.instance.playCrashSound();

    // Vibrate
    GameHelpers.heavyVibrate();

    print('Game over! Final score: $score');
  }

  /// Reset game statistics
  void _resetGameStats() {
    score = 0;
    distanceTraveled = 0.0;
    coinsCollected = 0;
    scoreMultiplier = 1;
    gameSpeed = GameConstants.gameSpeed;

    // Clear all game objects
    _clearGameObjects();

    // Reset player position
    player.resetPosition();
  }

  /// Clear all game objects
  void _clearGameObjects() {
    for (final obstacle in obstacles) {
      obstacle.removeFromParent();
    }
    obstacles.clear();

    for (final coin in coins) {
      coin.removeFromParent();
    }
    coins.clear();

    for (final powerUp in powerUps) {
      powerUp.removeFromParent();
    }
    powerUps.clear();
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (gameState == GameConstants.gameStatePlaying) {
      // Update distance traveled
      distanceTraveled += gameSpeed * dt;

      // Update score based on distance
      score = GameHelpers.calculateScore(
        distanceTraveled,
        coinsCollected,
        scoreMultiplier,
      );

      // Update UI
      _updateUI();

      // Update timers
      obstacleSpawnTimer.update(dt);
      coinSpawnTimer.update(dt);
      powerUpSpawnTimer.update(dt);
      gameSpeedTimer.update(dt);

      // Check collisions
      collisionDetector.checkCollisions();

      // Move background
      _moveBackground(dt);
    }
  }

  /// Update UI text components
  void _updateUI() {
    scoreText.text = 'Score: ${GameHelpers.formatScore(score)}';
    distanceText.text = 'Distance: ${(distanceTraveled / 10).floor()}m';
    coinText.text = 'Coins: $coinsCollected';
  }

  /// Move background for scrolling effect
  void _moveBackground(double dt) {
    // This is a placeholder for background scrolling
    // In a real implementation, you would move background sprites
  }

  /// Spawn obstacle
  void _spawnObstacle() {
    final obstacle = Obstacle(
      trackPosition: GameHelpers.getRandomTrackPosition(),
      gameSpeed: gameSpeed,
    );
    obstacles.add(obstacle);
    add(obstacle);
  }

  /// Spawn coin
  void _spawnCoin() {
    final coin = Coin(
      trackPosition: GameHelpers.getRandomTrackPosition(),
      gameSpeed: gameSpeed,
    );
    coins.add(coin);
    add(coin);
  }

  /// Spawn power-up
  void _spawnPowerUp() {
    final powerUp = PowerUp(
      trackPosition: GameHelpers.getRandomTrackPosition(),
      powerUpType: GameHelpers.getRandomPowerUpType(),
      gameSpeed: gameSpeed,
    );
    powerUps.add(powerUp);
    add(powerUp);
  }

  /// Increase game speed over time
  void _increaseGameSpeed() {
    gameSpeed = GameHelpers.calculateGameSpeed(distanceTraveled);

    // Update spawn rates
    final newSpawnRate = GameHelpers.calculateSpawnRate(distanceTraveled);
    obstacleSpawnTimer.limit = newSpawnRate;
  }

  /// Handle coin collection
  void collectCoin(Coin coin) {
    coinsCollected++;
    coins.remove(coin);
    coin.removeFromParent();
    AudioService.instance.playCoinSound();
    GameHelpers.lightVibrate();
  }

  /// Handle power-up collection
  void collectPowerUp(PowerUp powerUp) {
    powerUps.remove(powerUp);
    powerUp.removeFromParent();
    AudioService.instance.playPowerUpSound();
    GameHelpers.lightVibrate();

    // Apply power-up effect
    _applyPowerUpEffect(powerUp.powerUpType);
  }

  /// Apply power-up effect
  void _applyPowerUpEffect(PowerUpType type) {
    gameController.activatePowerUp(type);

    switch (type) {
      case PowerUpType.magnet:
        // Magnet effect is handled in collision detector
        break;
      case PowerUpType.shield:
        player.activateShield(GameConstants.shieldDuration);
        break;
      case PowerUpType.multiplier:
        scoreMultiplier = 2;
        // Reset multiplier after duration
        Timer(
          GameConstants.multiplierDuration,
          onTick: () {
            scoreMultiplier = 1;
          },
        );
        break;
      case PowerUpType.jetpack:
        _activateJetpack();
        break;
    }
  }

  /// Activate jetpack effect
  void _activateJetpack() {
    // Make player fly for a duration
    player.isJumping = true;
    player.jumpVelocity = -200.0; // Keep player in air

    Timer(
      3.0,
      onTick: () {
        // End jetpack effect
        player.jumpVelocity = 0.0;
      },
    );
  }

  /// Handle obstacle collision
  void hitObstacle(Obstacle obstacle) {
    endGame();
  }
}
