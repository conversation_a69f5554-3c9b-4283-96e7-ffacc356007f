import 'dart:math' as dart_math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';

import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../services/audio_service.dart';

/// Player component for Metro Dash
class Player extends SpriteComponent with CollisionCallbacks {
  // Player state
  TrackPosition currentTrack = TrackPosition.center;
  TrackPosition targetTrack = TrackPosition.center;
  bool isJumping = false;
  bool isSliding = false;
  bool isMoving = false;
  bool hasShield = false;

  // Movement properties
  double moveSpeed = 300.0;
  double jumpVelocity = 0.0;
  double groundY = 0.0;
  double slideTimer = 0.0;
  double shieldTimer = 0.0;

  // Animation properties
  late Timer animationTimer;
  int currentFrame = 0;
  final int totalFrames = 4;

  // Visual effects
  late RectangleComponent shieldEffect;
  bool shieldVisible = false;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Set player size and position
    size = Vector2(GameConstants.playerWidth, GameConstants.playerHeight);
    groundY = GameConstants.gameHeight - 100 - size.y;
    position = Vector2(GameConstants.centerTrackX - size.x / 2, groundY);

    // Create player sprite (placeholder colored rectangle)
    paint = Paint()..color = Colors.blue;

    // Add collision detection
    add(RectangleHitbox());

    // Setup animation timer
    animationTimer = Timer(
      0.2, // Animation frame duration
      onTick: _updateAnimation,
      repeat: true,
    );
    animationTimer.start();

    // Create shield effect
    await _createShieldEffect();

    print('Player loaded at position: $position');
  }

  /// Create shield visual effect
  Future<void> _createShieldEffect() async {
    shieldEffect = RectangleComponent(
      size: Vector2(size.x + 10, size.y + 10),
      position: Vector2(-5, -5),
      paint: Paint()
        ..color = Colors.green.withValues(alpha: 0.5)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3,
    );
    shieldEffect.opacity = 0.0;
    await add(shieldEffect);
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Update timers
    animationTimer.update(dt);

    // Update movement
    _updateMovement(dt);

    // Update jumping
    _updateJumping(dt);

    // Update sliding
    _updateSliding(dt);

    // Update shield
    _updateShield(dt);

    // Update visual effects
    _updateVisualEffects(dt);
  }

  /// Update horizontal movement between tracks
  void _updateMovement(double dt) {
    if (isMoving) {
      final targetX = targetTrack.xPosition - size.x / 2;
      final currentX = position.x;
      final distance = targetX - currentX;

      if (distance.abs() < 5.0) {
        // Reached target position
        position.x = targetX;
        currentTrack = targetTrack;
        isMoving = false;
      } else {
        // Move towards target
        final moveDirection = distance > 0 ? 1 : -1;
        position.x += moveDirection * moveSpeed * dt;
      }
    }
  }

  /// Update jumping physics
  void _updateJumping(double dt) {
    if (isJumping) {
      // Apply gravity
      jumpVelocity += GameConstants.gravity * dt;
      position.y += jumpVelocity * dt;

      // Check if landed
      if (position.y >= groundY) {
        position.y = groundY;
        isJumping = false;
        jumpVelocity = 0.0;
      }
    }
  }

  /// Update sliding state
  void _updateSliding(double dt) {
    if (isSliding) {
      slideTimer -= dt;
      if (slideTimer <= 0) {
        isSliding = false;
        size.y = GameConstants.playerHeight;
        position.y = groundY;
      }
    }
  }

  /// Update shield effect
  void _updateShield(double dt) {
    if (hasShield) {
      shieldTimer -= dt;
      if (shieldTimer <= 0) {
        hasShield = false;
        shieldEffect.opacity = 0.0;
      }
    }
  }

  /// Update visual effects
  void _updateVisualEffects(double dt) {
    // Shield pulsing effect
    if (hasShield) {
      final pulse = (DateTime.now().millisecondsSinceEpoch % 1000) / 1000.0;
      final opacity =
          0.3 + 0.3 * (1 + dart_math.sin(pulse * 2 * dart_math.pi)) / 2;
      shieldEffect.paint.color = Colors.green.withValues(alpha: opacity);
    }
  }

  /// Update animation frames
  void _updateAnimation() {
    currentFrame = (currentFrame + 1) % totalFrames;

    // Change color slightly for animation effect
    final colorValue = 150 + (currentFrame * 25);
    paint = Paint()..color = Color.fromARGB(255, 0, 0, colorValue);
  }

  /// Move player left
  void moveLeft() {
    if (isMoving) return;

    final newTrack = GameHelpers.getPreviousTrackPosition(currentTrack);
    if (newTrack != null) {
      targetTrack = newTrack;
      isMoving = true;
      AudioService.instance.playWhooshSound();
      GameHelpers.lightVibrate();
    }
  }

  /// Move player right
  void moveRight() {
    if (isMoving) return;

    final newTrack = GameHelpers.getNextTrackPosition(currentTrack);
    if (newTrack != null) {
      targetTrack = newTrack;
      isMoving = true;
      AudioService.instance.playWhooshSound();
      GameHelpers.lightVibrate();
    }
  }

  /// Make player jump
  void jump() {
    if (isJumping || isSliding) return;

    isJumping = true;
    jumpVelocity = -GameConstants.jumpForce;
    AudioService.instance.playJumpSound();
    GameHelpers.lightVibrate();
  }

  /// Make player slide
  void slide() {
    if (isJumping || isSliding) return;

    isSliding = true;
    slideTimer = 1.0; // Slide for 1 second
    size.y = GameConstants.playerHeight / 2;
    position.y = groundY + GameConstants.playerHeight / 2;
    AudioService.instance.playWhooshSound();
    GameHelpers.lightVibrate();
  }

  /// Activate shield power-up
  void activateShield(double duration) {
    hasShield = true;
    shieldTimer = duration;
    shieldEffect.opacity = 1.0;
  }

  /// Check if player has shield
  bool get isShielded => hasShield;

  /// Get player's collision rectangle
  Rect get collisionRect {
    return Rect.fromLTWH(position.x, position.y, size.x, size.y);
  }

  /// Reset player to initial position
  void resetPosition() {
    currentTrack = TrackPosition.center;
    targetTrack = TrackPosition.center;
    position = Vector2(GameConstants.centerTrackX - size.x / 2, groundY);
    isJumping = false;
    isSliding = false;
    isMoving = false;
    hasShield = false;
    jumpVelocity = 0.0;
    slideTimer = 0.0;
    shieldTimer = 0.0;
    size.y = GameConstants.playerHeight;
    shieldEffect.opacity = 0.0;
  }

  /// Handle collision with other components
  @override
  bool onCollision(Set<Vector2> intersectionPoints, PositionComponent other) {
    super.onCollision(intersectionPoints, other);
    // Collision handling will be implemented in the collision detector
    return true;
  }

  /// Get current track index for easy access
  int get trackIndex => currentTrack.index;

  /// Check if player is on ground
  bool get isOnGround => !isJumping && position.y >= groundY - 1;

  /// Get player center position
  Vector2 get centerPosition => position + size / 2;
}
