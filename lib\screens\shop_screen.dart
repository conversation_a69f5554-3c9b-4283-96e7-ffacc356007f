import 'package:flutter/material.dart';

import '../models/player_data.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../services/audio_service.dart';
import '../services/storage_service.dart';

/// Shop screen for purchasing characters and power-ups
class ShopScreen extends StatefulWidget {
  final PlayerData playerData;

  const ShopScreen({
    super.key,
    required this.playerData,
  });

  @override
  State<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends State<ShopScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PlayerData playerData;

  // Shop items
  final List<ShopCharacter> characters = [
    ShopCharacter(
      id: 'default',
      name: 'Runner',
      description: 'The classic runner',
      price: 0,
      color: Colors.blue,
      isUnlocked: true,
    ),
    ShopCharacter(
      id: 'speedster',
      name: '<PERSON><PERSON>',
      description: 'Faster movement speed',
      price: 500,
      color: Colors.red,
    ),
    ShopCharacter(
      id: 'jumper',
      name: 'High Jumper',
      description: 'Higher jump ability',
      price: 750,
      color: Colors.green,
    ),
    ShopCharacter(
      id: 'slider',
      name: 'Slider',
      description: 'Longer slide duration',
      price: 600,
      color: Colors.purple,
    ),
    ShopCharacter(
      id: 'lucky',
      name: 'Lucky',
      description: 'More coins spawn',
      price: 1000,
      color: Colors.orange,
    ),
  ];

  final List<ShopPowerUp> powerUps = [
    ShopPowerUp(
      type: PowerUpType.magnet,
      name: 'Magnet',
      description: 'Attracts coins from further away',
      basePrice: 100,
    ),
    ShopPowerUp(
      type: PowerUpType.shield,
      name: 'Shield',
      description: 'Lasts longer when activated',
      basePrice: 150,
    ),
    ShopPowerUp(
      type: PowerUpType.multiplier,
      name: 'Score Multiplier',
      description: 'Higher score multiplication',
      basePrice: 200,
    ),
    ShopPowerUp(
      type: PowerUpType.jetpack,
      name: 'Jetpack',
      description: 'Longer flight duration',
      basePrice: 250,
    ),
  ];

  @override
  void initState() {
    super.initState();
    playerData = widget.playerData;
    _tabController = TabController(length: 2, vsync: this);
    _updateCharacterUnlockStatus();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Update character unlock status
  void _updateCharacterUnlockStatus() {
    for (final character in characters) {
      character.isUnlocked = playerData.isCharacterUnlocked(character.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: GameConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Shop',
          style: TextStyle(
            color: GameConstants.textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: GameConstants.textColor),
          onPressed: () {
            AudioService.instance.playButtonClickSound();
            Navigator.pop(context);
          },
        ),
        actions: [
          _buildCoinDisplay(),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildCharactersTab(),
                  _buildPowerUpsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build coin display
  Widget _buildCoinDisplay() {
    return Container(
      margin: const EdgeInsets.only(right: 16),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.monetization_on,
            color: GameConstants.coinColor,
            size: 20,
          ),
          const SizedBox(width: 4),
          Text(
            '${playerData.totalCoins}',
            style: const TextStyle(
              color: GameConstants.coinColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Build tab bar
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(25),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: GameConstants.primaryColor,
          borderRadius: BorderRadius.circular(25),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: GameConstants.textColor.withValues(alpha: 0.7),
        tabs: const [
          Tab(text: 'Characters'),
          Tab(text: 'Power-ups'),
        ],
      ),
    );
  }

  /// Build characters tab
  Widget _buildCharactersTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: characters.length,
      itemBuilder: (context, index) {
        final character = characters[index];
        return _buildCharacterCard(character);
      },
    );
  }

  /// Build power-ups tab
  Widget _buildPowerUpsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: powerUps.length,
      itemBuilder: (context, index) {
        final powerUp = powerUps[index];
        return _buildPowerUpCard(powerUp);
      },
    );
  }

  /// Build character card
  Widget _buildCharacterCard(ShopCharacter character) {
    final isSelected = playerData.selectedCharacter == character.id;
    final canAfford = playerData.totalCoins >= character.price;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected
              ? GameConstants.primaryColor
              : character.color.withValues(alpha: 0.3),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          // Character avatar
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: character.color,
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Icon(
              Icons.person,
              color: Colors.white,
              size: 30,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Character info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  character.name,
                  style: const TextStyle(
                    color: GameConstants.textColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  character.description,
                  style: TextStyle(
                    color: GameConstants.textColor.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
                if (isSelected)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: GameConstants.primaryColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      'SELECTED',
                      style: TextStyle(
                        color: GameConstants.primaryColor,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          
          // Action button
          _buildCharacterButton(character, canAfford, isSelected),
        ],
      ),
    );
  }

  /// Build character action button
  Widget _buildCharacterButton(ShopCharacter character, bool canAfford, bool isSelected) {
    if (isSelected) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: GameConstants.primaryColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: GameConstants.primaryColor),
        ),
        child: Text(
          'EQUIPPED',
          style: TextStyle(
            color: GameConstants.primaryColor,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    if (!character.isUnlocked) {
      return ElevatedButton(
        onPressed: canAfford ? () => _purchaseCharacter(character) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: canAfford ? GameConstants.secondaryColor : Colors.grey,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.monetization_on, size: 16),
            const SizedBox(width: 4),
            Text('${character.price}'),
          ],
        ),
      );
    }

    return ElevatedButton(
      onPressed: () => _selectCharacter(character),
      style: ElevatedButton.styleFrom(
        backgroundColor: GameConstants.primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
      child: const Text('SELECT'),
    );
  }

  /// Build power-up card
  Widget _buildPowerUpCard(ShopPowerUp powerUp) {
    final currentLevel = playerData.getPowerUpLevel(powerUp.type.name.toLowerCase());
    final upgradePrice = _calculateUpgradePrice(powerUp.basePrice, currentLevel);
    final canAfford = playerData.totalCoins >= upgradePrice;
    final maxLevel = currentLevel >= 5;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: powerUp.type.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Power-up icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: powerUp.type.color,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              _getPowerUpIcon(powerUp.type),
              color: Colors.white,
              size: 30,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Power-up info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      powerUp.name,
                      style: const TextStyle(
                        color: GameConstants.textColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Lv.$currentLevel',
                      style: TextStyle(
                        color: powerUp.type.color,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  powerUp.description,
                  style: TextStyle(
                    color: GameConstants.textColor.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
                // Level indicator
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  child: Row(
                    children: List.generate(5, (index) {
                      return Container(
                        width: 20,
                        height: 4,
                        margin: const EdgeInsets.only(right: 4),
                        decoration: BoxDecoration(
                          color: index < currentLevel
                              ? powerUp.type.color
                              : Colors.grey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      );
                    }),
                  ),
                ),
              ],
            ),
          ),
          
          // Upgrade button
          if (!maxLevel)
            ElevatedButton(
              onPressed: canAfford ? () => _upgradePowerUp(powerUp) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: canAfford ? powerUp.type.color : Colors.grey,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.monetization_on, size: 16),
                  const SizedBox(width: 4),
                  Text('$upgradePrice'),
                ],
              ),
            )
          else
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.green),
              ),
              child: const Text(
                'MAX',
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Get power-up icon
  IconData _getPowerUpIcon(PowerUpType type) {
    switch (type) {
      case PowerUpType.magnet:
        return Icons.attractions;
      case PowerUpType.shield:
        return Icons.shield;
      case PowerUpType.multiplier:
        return Icons.close;
      case PowerUpType.jetpack:
        return Icons.flight;
    }
  }

  /// Calculate upgrade price
  int _calculateUpgradePrice(int basePrice, int currentLevel) {
    return (basePrice * (currentLevel + 1) * 1.5).round();
  }

  /// Purchase character
  void _purchaseCharacter(ShopCharacter character) async {
    if (playerData.spendCoins(character.price)) {
      character.isUnlocked = true;
      playerData.unlockCharacter(character.id);
      
      await StorageService.instance.savePlayerData(playerData);
      
      setState(() {});
      
      AudioService.instance.playPowerUpSound();
      GameHelpers.showMessage(context, '${character.name} unlocked!');
    }
  }

  /// Select character
  void _selectCharacter(ShopCharacter character) async {
    playerData.selectedCharacter = character.id;
    await StorageService.instance.savePlayerData(playerData);
    
    setState(() {});
    
    AudioService.instance.playButtonClickSound();
    GameHelpers.showMessage(context, '${character.name} selected!');
  }

  /// Upgrade power-up
  void _upgradePowerUp(ShopPowerUp powerUp) async {
    final currentLevel = playerData.getPowerUpLevel(powerUp.type.name.toLowerCase());
    final upgradePrice = _calculateUpgradePrice(powerUp.basePrice, currentLevel);
    
    if (playerData.upgradePowerUp(powerUp.type.name.toLowerCase(), upgradePrice)) {
      await StorageService.instance.savePlayerData(playerData);
      
      setState(() {});
      
      AudioService.instance.playPowerUpSound();
      GameHelpers.showMessage(context, '${powerUp.name} upgraded!');
    }
  }
}

/// Shop character model
class ShopCharacter {
  final String id;
  final String name;
  final String description;
  final int price;
  final Color color;
  bool isUnlocked;

  ShopCharacter({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.color,
    this.isUnlocked = false,
  });
}

/// Shop power-up model
class ShopPowerUp {
  final PowerUpType type;
  final String name;
  final String description;
  final int basePrice;

  ShopPowerUp({
    required this.type,
    required this.name,
    required this.description,
    required this.basePrice,
  });
}
