import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'constants.dart';

/// Helper utilities for Metro Dash game
class GameHelpers {
  static final Random _random = Random();
  
  /// Generate random track position
  static TrackPosition getRandomTrackPosition() {
    final positions = TrackPosition.values;
    return positions[_random.nextInt(positions.length)];
  }
  
  /// Generate random power-up type
  static PowerUpType getRandomPowerUpType() {
    final types = PowerUpType.values;
    return types[_random.nextInt(types.length)];
  }
  
  /// Get random double between min and max
  static double randomDouble(double min, double max) {
    return min + _random.nextDouble() * (max - min);
  }
  
  /// Get random integer between min and max (inclusive)
  static int randomInt(int min, int max) {
    return min + _random.nextInt(max - min + 1);
  }
  
  /// Format score with commas
  static String formatScore(int score) {
    return score.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }
  
  /// Format time in MM:SS format
  static String formatTime(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
  
  /// Calculate distance traveled based on time and speed
  static double calculateDistance(Duration time, double speed) {
    return time.inMilliseconds / 1000.0 * speed;
  }
  
  /// Calculate current game speed based on distance
  static double calculateGameSpeed(double distance) {
    final baseSpeed = GameConstants.gameSpeed;
    final speedIncrease = distance / 1000.0 * 50.0; // Increase speed every 1000 units
    return baseSpeed + speedIncrease;
  }
  
  /// Calculate obstacle spawn rate based on difficulty
  static double calculateSpawnRate(double distance) {
    final baseRate = GameConstants.obstacleSpawnRate;
    final rateDecrease = distance / 2000.0 * 0.5; // Decrease spawn time every 2000 units
    return (baseRate - rateDecrease).clamp(0.5, baseRate);
  }
  
  /// Get difficulty level based on distance
  static DifficultyLevel getDifficultyLevel(double distance) {
    if (distance < 1000) return DifficultyLevel.easy;
    if (distance < 3000) return DifficultyLevel.medium;
    if (distance < 6000) return DifficultyLevel.hard;
    return DifficultyLevel.expert;
  }
  
  /// Vibrate device for feedback
  static void vibrate({Duration duration = const Duration(milliseconds: 100)}) {
    HapticFeedback.mediumImpact();
  }
  
  /// Light vibration for UI feedback
  static void lightVibrate() {
    HapticFeedback.lightImpact();
  }
  
  /// Heavy vibration for crashes
  static void heavyVibrate() {
    HapticFeedback.heavyImpact();
  }
  
  /// Check if two rectangles collide
  static bool checkCollision(
    double x1, double y1, double w1, double h1,
    double x2, double y2, double w2, double h2,
  ) {
    return x1 < x2 + w2 &&
           x1 + w1 > x2 &&
           y1 < y2 + h2 &&
           y1 + h1 > y2;
  }
  
  /// Check if point is inside rectangle
  static bool pointInRect(double px, double py, double rx, double ry, double rw, double rh) {
    return px >= rx && px <= rx + rw && py >= ry && py <= ry + rh;
  }
  
  /// Lerp between two values
  static double lerp(double a, double b, double t) {
    return a + (b - a) * t;
  }
  
  /// Clamp value between min and max
  static double clamp(double value, double min, double max) {
    return value < min ? min : (value > max ? max : value);
  }
  
  /// Convert track position to x coordinate
  static double trackPositionToX(TrackPosition position) {
    return position.xPosition;
  }
  
  /// Convert x coordinate to nearest track position
  static TrackPosition xToTrackPosition(double x) {
    final leftDistance = (x - GameConstants.leftTrackX).abs();
    final centerDistance = (x - GameConstants.centerTrackX).abs();
    final rightDistance = (x - GameConstants.rightTrackX).abs();
    
    if (leftDistance <= centerDistance && leftDistance <= rightDistance) {
      return TrackPosition.left;
    } else if (centerDistance <= rightDistance) {
      return TrackPosition.center;
    } else {
      return TrackPosition.right;
    }
  }
  
  /// Get next track position (for moving right)
  static TrackPosition? getNextTrackPosition(TrackPosition current) {
    switch (current) {
      case TrackPosition.left:
        return TrackPosition.center;
      case TrackPosition.center:
        return TrackPosition.right;
      case TrackPosition.right:
        return null; // Already at rightmost position
    }
  }
  
  /// Get previous track position (for moving left)
  static TrackPosition? getPreviousTrackPosition(TrackPosition current) {
    switch (current) {
      case TrackPosition.left:
        return null; // Already at leftmost position
      case TrackPosition.center:
        return TrackPosition.left;
      case TrackPosition.right:
        return TrackPosition.center;
    }
  }
  
  /// Calculate score based on distance and coins
  static int calculateScore(double distance, int coins, int multiplier) {
    final distanceScore = (distance / 10).floor();
    final coinScore = coins * GameConstants.coinValue;
    return (distanceScore + coinScore) * multiplier;
  }
  
  /// Show snackbar message
  static void showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        backgroundColor: GameConstants.primaryColor,
      ),
    );
  }
}
