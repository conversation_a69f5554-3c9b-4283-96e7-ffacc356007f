import 'package:flutter/material.dart';
import 'package:flame/game.dart';

import '../game.dart';
import '../models/player_data.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../services/audio_service.dart';
import '../controllers/input_handler.dart';
import 'game_over_screen.dart';

/// Game screen that contains the Flame game
class GameScreen extends StatefulWidget {
  final PlayerData playerData;

  const GameScreen({super.key, required this.playerData});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with WidgetsBindingObserver {
  late MetroDashGame game;
  bool isPaused = false;
  bool isGameOver = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeGame();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// Initialize the game
  void _initializeGame() {
    game = MetroDashGame();

    // Listen for game state changes
    game.gameController.initialize().then((_) {
      // Start the game after initialization
      game.gameController.startGame();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        if (!isPaused && !isGameOver) {
          _pauseGame();
        }
        break;
      case AppLifecycleState.resumed:
        // Game will be resumed manually by user
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Game widget
          GameWidget<MetroDashGame>.controlled(gameFactory: () => game),

          // Game UI overlay
          _buildGameUI(),

          // Touch controls overlay
          if (!isPaused && !isGameOver) _buildTouchControls(),

          // Pause overlay
          if (isPaused) _buildPauseOverlay(),

          // Game over overlay
          if (isGameOver) _buildGameOverOverlay(),
        ],
      ),
    );
  }

  /// Build game UI overlay
  Widget _buildGameUI() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Top UI
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Score and stats
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatText(
                      'Score: ${GameHelpers.formatScore(game.score)}',
                      GameConstants.textColor,
                    ),
                    _buildStatText(
                      'Distance: ${(game.distanceTraveled / 10).floor()}m',
                      GameConstants.textColor,
                    ),
                  ],
                ),

                // Coins and pause button
                Row(
                  children: [
                    _buildCoinDisplay(),
                    const SizedBox(width: 16),
                    _buildPauseButton(),
                  ],
                ),
              ],
            ),

            const Spacer(),

            // Bottom UI - Controls hint
            if (!isPaused && !isGameOver) _buildControlsHint(),
          ],
        ),
      ),
    );
  }

  /// Build stat text
  Widget _buildStatText(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          shadows: const [
            Shadow(offset: Offset(1, 1), blurRadius: 2, color: Colors.black),
          ],
        ),
      ),
    );
  }

  /// Build coin display
  Widget _buildCoinDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.monetization_on,
            color: GameConstants.coinColor,
            size: 20,
          ),
          const SizedBox(width: 4),
          Text(
            '${game.coinsCollected}',
            style: const TextStyle(
              color: GameConstants.coinColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  offset: Offset(1, 1),
                  blurRadius: 2,
                  color: Colors.black,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build pause button
  Widget _buildPauseButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(25),
      ),
      child: IconButton(
        icon: const Icon(Icons.pause, color: GameConstants.textColor, size: 24),
        onPressed: _pauseGame,
      ),
    );
  }

  /// Build controls hint
  Widget _buildControlsHint() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Tap to control:',
            style: TextStyle(
              color: GameConstants.textColor.withValues(alpha: 0.8),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlHint('←', 'Left'),
              _buildControlHint('→', 'Right'),
              _buildControlHint('↑', 'Jump'),
              _buildControlHint('↓', 'Slide'),
            ],
          ),
        ],
      ),
    );
  }

  /// Build control hint
  Widget _buildControlHint(String symbol, String action) {
    return Column(
      children: [
        Text(
          symbol,
          style: const TextStyle(
            color: GameConstants.textColor,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          action,
          style: TextStyle(
            color: GameConstants.textColor.withValues(alpha: 0.7),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// Build pause overlay
  Widget _buildPauseOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          margin: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: GameConstants.backgroundColor,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: GameConstants.primaryColor.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'PAUSED',
                style: TextStyle(
                  color: GameConstants.textColor,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 32),

              // Resume button
              _buildPauseMenuButton(
                'RESUME',
                Icons.play_arrow,
                GameConstants.primaryColor,
                _resumeGame,
              ),

              const SizedBox(height: 16),

              // Restart button
              _buildPauseMenuButton(
                'RESTART',
                Icons.refresh,
                GameConstants.secondaryColor,
                _restartGame,
              ),

              const SizedBox(height: 16),

              // Home button
              _buildPauseMenuButton(
                'HOME',
                Icons.home,
                Colors.grey[600]!,
                _goHome,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build pause menu button
  Widget _buildPauseMenuButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      width: 200,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: () {
          AudioService.instance.playButtonClickSound();
          GameHelpers.lightVibrate();
          onPressed();
        },
        icon: Icon(icon, color: Colors.white),
        label: Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
      ),
    );
  }

  /// Build touch controls overlay
  Widget _buildTouchControls() {
    return TouchControlOverlay(
      onLeftTap: () => game.player.moveLeft(),
      onRightTap: () => game.player.moveRight(),
      onJumpTap: () => game.player.jump(),
      onSlideTap: () => game.player.slide(),
      showControls: true,
    );
  }

  /// Build game over overlay
  Widget _buildGameOverOverlay() {
    return GameOverScreen(
      score: game.score,
      coins: game.coinsCollected,
      distance: game.distanceTraveled,
      playerData: widget.playerData,
      onRestart: _restartGame,
      onHome: _goHome,
    );
  }

  /// Pause the game
  void _pauseGame() {
    if (!isPaused && !isGameOver) {
      setState(() {
        isPaused = true;
      });
      game.gameController.pauseGame();
    }
  }

  /// Resume the game
  void _resumeGame() {
    if (isPaused) {
      setState(() {
        isPaused = false;
      });
      game.gameController.resumeGame();
    }
  }

  /// Restart the game
  void _restartGame() {
    setState(() {
      isPaused = false;
      isGameOver = false;
    });

    // Create new game instance
    _initializeGame();
  }

  /// Go to home screen
  void _goHome() {
    Navigator.of(context).pop();
  }

  /// Handle game over
  void _onGameOver() {
    setState(() {
      isGameOver = true;
    });
  }
}
