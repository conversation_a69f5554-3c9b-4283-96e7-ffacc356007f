import 'package:flutter/material.dart';

/// Game Constants for Metro Dash
class GameConstants {
  // Game Settings
  static const String gameTitle = 'Metro Dash';
  static const double gameSpeed = 200.0;
  static const double gravity = 980.0;
  static const double jumpForce = 400.0;
  
  // Screen Dimensions
  static const double gameWidth = 400.0;
  static const double gameHeight = 800.0;
  
  // Player Settings
  static const double playerWidth = 40.0;
  static const double playerHeight = 60.0;
  static const double playerSpeed = 300.0;
  
  // Track Settings
  static const int numberOfTracks = 3;
  static const double trackWidth = gameWidth / numberOfTracks;
  static const double leftTrackX = trackWidth / 2;
  static const double centerTrackX = gameWidth / 2;
  static const double rightTrackX = gameWidth - (trackWidth / 2);
  
  // Obstacle Settings
  static const double obstacleWidth = 60.0;
  static const double obstacleHeight = 80.0;
  static const double obstacleSpawnRate = 2.0; // seconds
  
  // Coin Settings
  static const double coinSize = 30.0;
  static const int coinValue = 10;
  
  // Power-up Settings
  static const double powerUpSize = 40.0;
  static const double magnetDuration = 5.0;
  static const double shieldDuration = 3.0;
  static const double multiplierDuration = 10.0;
  
  // UI Colors
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color secondaryColor = Color(0xFFFF9800);
  static const Color backgroundColor = Color(0xFF1A1A1A);
  static const Color textColor = Color(0xFFFFFFFF);
  static const Color coinColor = Color(0xFFFFD700);
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 500);
  static const Duration longAnimation = Duration(milliseconds: 1000);
  
  // Storage Keys
  static const String highScoreKey = 'high_score';
  static const String coinsKey = 'total_coins';
  static const String soundEnabledKey = 'sound_enabled';
  static const String musicEnabledKey = 'music_enabled';
  static const String selectedCharacterKey = 'selected_character';
  
  // Game States
  static const String gameStateMenu = 'menu';
  static const String gameStatePlaying = 'playing';
  static const String gameStatePaused = 'paused';
  static const String gameStateGameOver = 'game_over';
  
  // Audio Files
  static const String backgroundMusic = 'audio/background_music.mp3';
  static const String jumpSound = 'audio/jump.wav';
  static const String coinSound = 'audio/coin.wav';
  static const String crashSound = 'audio/crash.wav';
  static const String powerUpSound = 'audio/powerup.wav';
}

/// Track positions enum
enum TrackPosition {
  left,
  center,
  right,
}

/// Player actions enum
enum PlayerAction {
  moveLeft,
  moveRight,
  jump,
  slide,
}

/// Power-up types enum
enum PowerUpType {
  magnet,
  shield,
  multiplier,
  jetpack,
}

/// Game difficulty levels
enum DifficultyLevel {
  easy,
  medium,
  hard,
  expert,
}

/// Extension methods for enums
extension TrackPositionExtension on TrackPosition {
  double get xPosition {
    switch (this) {
      case TrackPosition.left:
        return GameConstants.leftTrackX;
      case TrackPosition.center:
        return GameConstants.centerTrackX;
      case TrackPosition.right:
        return GameConstants.rightTrackX;
    }
  }
  
  int get index {
    switch (this) {
      case TrackPosition.left:
        return 0;
      case TrackPosition.center:
        return 1;
      case TrackPosition.right:
        return 2;
    }
  }
}

extension PowerUpTypeExtension on PowerUpType {
  String get name {
    switch (this) {
      case PowerUpType.magnet:
        return 'Magnet';
      case PowerUpType.shield:
        return 'Shield';
      case PowerUpType.multiplier:
        return 'Multiplier';
      case PowerUpType.jetpack:
        return 'Jetpack';
    }
  }
  
  Color get color {
    switch (this) {
      case PowerUpType.magnet:
        return Colors.blue;
      case PowerUpType.shield:
        return Colors.green;
      case PowerUpType.multiplier:
        return Colors.orange;
      case PowerUpType.jetpack:
        return Colors.purple;
    }
  }
}
