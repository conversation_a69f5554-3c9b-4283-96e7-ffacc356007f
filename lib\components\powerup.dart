import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flame/effects.dart';
import 'package:flutter/material.dart';

import '../utils/constants.dart';
import '../utils/helpers.dart';

/// Power-up component for Metro Dash
class PowerUp extends SpriteComponent with CollisionCallbacks {
  final TrackPosition trackPosition;
  final double gameSpeed;
  final PowerUpType powerUpType;

  // Movement and animation properties
  late double moveSpeed;
  bool isActive = true;
  bool isCollected = false;
  double rotationSpeed = 3.0;
  double pulseOffset = 0.0;
  double pulseSpeed = 4.0;
  double pulseAmplitude = 0.2;

  // Visual effects
  late Timer glowTimer;
  bool showGlow = false;
  late RectangleComponent glowEffect;

  PowerUp({
    required this.trackPosition,
    required this.gameSpeed,
    required this.powerUpType,
  }) {
    moveSpeed = gameSpeed;
  }

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Set power-up size
    size = Vector2(GameConstants.powerUpSize, GameConstants.powerUpSize);

    // Set position
    position = Vector2(
      trackPosition.xPosition - size.x / 2,
      -size.y, // Start above screen
    );

    // Set visual appearance
    _setPowerUpAppearance();

    // Add collision detection
    add(RectangleHitbox());

    // Create glow effect
    await _createGlowEffect();

    // Setup glow timer
    glowTimer = Timer(
      0.3, // Glow every 0.3 seconds
      onTick: () => showGlow = !showGlow,
      repeat: true,
    );
    glowTimer.start();

    print(
      'PowerUp ${powerUpType.name} created at track ${trackPosition.index}',
    );
  }

  /// Set power-up visual appearance
  void _setPowerUpAppearance() {
    paint = Paint()..color = powerUpType.color;
  }

  /// Create glow effect around power-up
  Future<void> _createGlowEffect() async {
    glowEffect = RectangleComponent(
      size: Vector2(size.x + 10, size.y + 10),
      position: Vector2(-5, -5),
      paint: Paint()
        ..color = powerUpType.color.withValues(alpha: 0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2,
    );
    glowEffect.opacity = 0.0;
    await add(glowEffect);
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (!isActive || isCollected) return;

    // Update timers
    glowTimer.update(dt);

    // Move power-up down
    position.y += moveSpeed * dt;

    // Rotate power-up
    angle += rotationSpeed * dt;

    // Pulse effect
    pulseOffset += pulseSpeed * dt;
    final pulseScale = 1.0 + math.sin(pulseOffset) * pulseAmplitude;
    scale = Vector2.all(pulseScale);

    // Update glow effect
    _updateGlowEffect(dt);

    // Remove if off screen
    if (position.y > GameConstants.gameHeight + size.y) {
      _removePowerUp();
    }
  }

  /// Update glow visual effect
  void _updateGlowEffect(double dt) {
    if (showGlow) {
      final glowIntensity = (math.sin(pulseOffset * 1.5) + 1) / 2;
      glowEffect.opacity = glowIntensity * 0.8;

      // Pulsing glow size
      final glowScale = 1.0 + glowIntensity * 0.3;
      glowEffect.scale = Vector2.all(glowScale);
    } else {
      glowEffect.opacity = 0.0;
    }
  }

  /// Collect the power-up
  void collect() {
    if (isCollected) return;

    isCollected = true;
    isActive = false;

    // Play collection animation
    _playCollectionAnimation();
  }

  /// Play collection animation
  void _playCollectionAnimation() {
    // Scale up and fade out
    final scaleEffect = ScaleEffect.to(
      Vector2.all(2.0),
      EffectController(duration: 0.4),
    );

    final fadeEffect = OpacityEffect.to(0.0, EffectController(duration: 0.4));

    // Rotation burst effect
    final rotateEffect = RotateEffect.by(
      math.pi * 2,
      EffectController(duration: 0.4),
    );

    add(scaleEffect);
    add(fadeEffect);
    add(rotateEffect);

    // Remove after animation
    Timer(0.4, onTick: () => _removePowerUp());
  }

  /// Remove power-up from game
  void _removePowerUp() {
    isActive = false;
    removeFromParent();
  }

  /// Handle collision with player
  @override
  bool onCollision(Set<Vector2> intersectionPoints, PositionComponent other) {
    super.onCollision(intersectionPoints, other);

    if (other.runtimeType.toString() == 'Player' && isActive && !isCollected) {
      collect();
      _handlePlayerCollection();
    }

    return true;
  }

  /// Handle player collection
  void _handlePlayerCollection() {
    // This will be handled by the collision detector in the game
    print('Player collected power-up: ${powerUpType.name}');
  }

  /// Get power-up duration
  double get duration => powerUpType.duration;

  /// Get collision rectangle
  Rect get collisionRect {
    return Rect.fromLTWH(position.x, position.y, size.x, size.y);
  }

  /// Check if power-up is visible on screen
  bool get isOnScreen {
    return position.y < GameConstants.gameHeight && position.y + size.y > 0;
  }

  /// Get power-up center position
  Vector2 get centerPosition => position + size / 2;

  /// Update movement speed
  void updateSpeed(double newSpeed) {
    moveSpeed = newSpeed;
  }

  /// Create floating text effect for power-up name
  void createNameText() {
    final nameText = TextComponent(
      text: powerUpType.name,
      position: centerPosition,
      textRenderer: TextPaint(
        style: TextStyle(
          color: powerUpType.color,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );

    // Add floating animation
    final moveEffect = MoveEffect.to(
      centerPosition + Vector2(0, -60),
      EffectController(duration: 1.5),
    );

    final fadeEffect = OpacityEffect.to(0.0, EffectController(duration: 1.5));

    nameText.add(moveEffect);
    nameText.add(fadeEffect);

    parent?.add(nameText);

    // Remove after animation
    Timer(1.5, onTick: () => nameText.removeFromParent());
  }

  /// Get power-up description
  String get description => powerUpType.description;

  /// Get power-up rarity
  double get rarity => powerUpType.rarity;
}

/// Extension for PowerUpType with additional properties
extension PowerUpTypeExtension on PowerUpType {
  double get duration {
    switch (this) {
      case PowerUpType.magnet:
        return GameConstants.magnetDuration;
      case PowerUpType.shield:
        return GameConstants.shieldDuration;
      case PowerUpType.multiplier:
        return GameConstants.multiplierDuration;
      case PowerUpType.jetpack:
        return 3.0;
    }
  }

  String get description {
    switch (this) {
      case PowerUpType.magnet:
        return 'Attracts nearby coins';
      case PowerUpType.shield:
        return 'Protects from one collision';
      case PowerUpType.multiplier:
        return 'Doubles your score';
      case PowerUpType.jetpack:
        return 'Fly over obstacles';
    }
  }

  double get rarity {
    switch (this) {
      case PowerUpType.magnet:
        return 0.4; // 40% chance
      case PowerUpType.shield:
        return 0.3; // 30% chance
      case PowerUpType.multiplier:
        return 0.2; // 20% chance
      case PowerUpType.jetpack:
        return 0.1; // 10% chance
    }
  }

  /// Get random power-up type based on rarity
  static PowerUpType getRandom() {
    final random = GameHelpers.randomDouble(0.0, 1.0);
    double cumulativeRarity = 0.0;

    for (final type in PowerUpType.values) {
      cumulativeRarity += type.rarity;
      if (random < cumulativeRarity) {
        return type;
      }
    }

    return PowerUpType.magnet; // Fallback
  }
}
