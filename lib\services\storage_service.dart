import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/player_data.dart';
import '../utils/constants.dart';

/// Storage service for persisting game data
class StorageService {
  static StorageService? _instance;
  static SharedPreferences? _prefs;
  
  StorageService._();
  
  /// Singleton instance
  static StorageService get instance {
    _instance ??= StorageService._();
    return _instance!;
  }
  
  /// Initialize the storage service
  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  /// Get SharedPreferences instance
  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call StorageService.initialize() first.');
    }
    return _prefs!;
  }
  
  /// Save player data
  Future<bool> savePlayerData(PlayerData playerData) async {
    try {
      final jsonString = jsonEncode(playerData.toJson());
      return await prefs.setString('player_data', jsonString);
    } catch (e) {
      print('Error saving player data: $e');
      return false;
    }
  }
  
  /// Load player data
  Future<PlayerData> loadPlayerData() async {
    try {
      final jsonString = prefs.getString('player_data');
      if (jsonString != null) {
        final jsonMap = jsonDecode(jsonString) as Map<String, dynamic>;
        return PlayerData.fromJson(jsonMap);
      }
    } catch (e) {
      print('Error loading player data: $e');
    }
    // Return default player data if loading fails
    return PlayerData();
  }
  
  /// Save high score
  Future<bool> saveHighScore(int score) async {
    return await prefs.setInt(GameConstants.highScoreKey, score);
  }
  
  /// Get high score
  int getHighScore() {
    return prefs.getInt(GameConstants.highScoreKey) ?? 0;
  }
  
  /// Save total coins
  Future<bool> saveTotalCoins(int coins) async {
    return await prefs.setInt(GameConstants.coinsKey, coins);
  }
  
  /// Get total coins
  int getTotalCoins() {
    return prefs.getInt(GameConstants.coinsKey) ?? 0;
  }
  
  /// Save sound setting
  Future<bool> saveSoundEnabled(bool enabled) async {
    return await prefs.setBool(GameConstants.soundEnabledKey, enabled);
  }
  
  /// Get sound setting
  bool getSoundEnabled() {
    return prefs.getBool(GameConstants.soundEnabledKey) ?? true;
  }
  
  /// Save music setting
  Future<bool> saveMusicEnabled(bool enabled) async {
    return await prefs.setBool(GameConstants.musicEnabledKey, enabled);
  }
  
  /// Get music setting
  bool getMusicEnabled() {
    return prefs.getBool(GameConstants.musicEnabledKey) ?? true;
  }
  
  /// Save selected character
  Future<bool> saveSelectedCharacter(String characterId) async {
    return await prefs.setString(GameConstants.selectedCharacterKey, characterId);
  }
  
  /// Get selected character
  String getSelectedCharacter() {
    return prefs.getString(GameConstants.selectedCharacterKey) ?? 'default';
  }
  
  /// Save unlocked characters
  Future<bool> saveUnlockedCharacters(List<String> characters) async {
    return await prefs.setStringList('unlocked_characters', characters);
  }
  
  /// Get unlocked characters
  List<String> getUnlockedCharacters() {
    return prefs.getStringList('unlocked_characters') ?? ['default'];
  }
  
  /// Save power-up levels
  Future<bool> savePowerUpLevels(Map<String, int> levels) async {
    final jsonString = jsonEncode(levels);
    return await prefs.setString('powerup_levels', jsonString);
  }
  
  /// Get power-up levels
  Map<String, int> getPowerUpLevels() {
    try {
      final jsonString = prefs.getString('powerup_levels');
      if (jsonString != null) {
        final Map<String, dynamic> decoded = jsonDecode(jsonString);
        return decoded.map((key, value) => MapEntry(key, value as int));
      }
    } catch (e) {
      print('Error loading power-up levels: $e');
    }
    return {
      'magnet': 1,
      'shield': 1,
      'multiplier': 1,
      'jetpack': 1,
    };
  }
  
  /// Save achievements
  Future<bool> saveAchievements(Map<String, bool> achievements) async {
    final jsonString = jsonEncode(achievements);
    return await prefs.setString('achievements', jsonString);
  }
  
  /// Get achievements
  Map<String, bool> getAchievements() {
    try {
      final jsonString = prefs.getString('achievements');
      if (jsonString != null) {
        final Map<String, dynamic> decoded = jsonDecode(jsonString);
        return decoded.map((key, value) => MapEntry(key, value as bool));
      }
    } catch (e) {
      print('Error loading achievements: $e');
    }
    return {
      'first_run': false,
      'collect_100_coins': false,
      'collect_1000_coins': false,
      'score_1000': false,
      'score_10000': false,
      'run_1000m': false,
      'run_5000m': false,
      'play_10_games': false,
      'play_100_games': false,
      'use_all_powerups': false,
      'unlock_all_characters': false,
    };
  }
  
  /// Save game statistics
  Future<bool> saveGameStats({
    required int gamesPlayed,
    required double totalDistance,
    required Duration totalPlayTime,
  }) async {
    final success1 = await prefs.setInt('games_played', gamesPlayed);
    final success2 = await prefs.setDouble('total_distance', totalDistance);
    final success3 = await prefs.setInt('total_play_time', totalPlayTime.inMilliseconds);
    return success1 && success2 && success3;
  }
  
  /// Get game statistics
  Map<String, dynamic> getGameStats() {
    return {
      'gamesPlayed': prefs.getInt('games_played') ?? 0,
      'totalDistance': prefs.getDouble('total_distance') ?? 0.0,
      'totalPlayTime': Duration(milliseconds: prefs.getInt('total_play_time') ?? 0),
    };
  }
  
  /// Clear all data (for reset functionality)
  Future<bool> clearAllData() async {
    try {
      await prefs.clear();
      return true;
    } catch (e) {
      print('Error clearing data: $e');
      return false;
    }
  }
  
  /// Check if this is the first time opening the app
  bool isFirstTime() {
    return prefs.getBool('first_time') ?? true;
  }
  
  /// Mark that the app has been opened before
  Future<bool> setNotFirstTime() async {
    return await prefs.setBool('first_time', false);
  }
  
  /// Save last played date
  Future<bool> saveLastPlayedDate() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    return await prefs.setInt('last_played', now);
  }
  
  /// Get last played date
  DateTime? getLastPlayedDate() {
    final timestamp = prefs.getInt('last_played');
    if (timestamp != null) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    }
    return null;
  }
  
  /// Check if daily reward is available
  bool isDailyRewardAvailable() {
    final lastPlayed = getLastPlayedDate();
    if (lastPlayed == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(lastPlayed);
    return difference.inHours >= 24;
  }
}
