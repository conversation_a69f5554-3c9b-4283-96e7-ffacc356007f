import 'package:flutter/material.dart';

import '../models/player_data.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../services/audio_service.dart';
import '../services/storage_service.dart';

/// Settings screen for game configuration
class SettingsScreen extends StatefulWidget {
  final PlayerData playerData;

  const SettingsScreen({
    super.key,
    required this.playerData,
  });

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late PlayerData playerData;

  @override
  void initState() {
    super.initState();
    playerData = widget.playerData;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: GameConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Settings',
          style: TextStyle(
            color: GameConstants.textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: GameConstants.textColor),
          onPressed: () {
            AudioService.instance.playButtonClickSound();
            Navigator.pop(context);
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildAudioSection(),
            const SizedBox(height: 20),
            _buildGameplaySection(),
            const SizedBox(height: 20),
            _buildAboutSection(),
          ],
        ),
      ),
    );
  }

  /// Build audio settings section
  Widget _buildAudioSection() {
    return _buildSection(
      'Audio Settings',
      Icons.volume_up,
      [
        _buildSwitchTile(
          'Sound Effects',
          'Enable sound effects during gameplay',
          Icons.music_note,
          playerData.soundEnabled,
          (value) => _toggleSound(value),
        ),
        _buildSwitchTile(
          'Background Music',
          'Enable background music',
          Icons.library_music,
          playerData.musicEnabled,
          (value) => _toggleMusic(value),
        ),
      ],
    );
  }

  /// Build gameplay settings section
  Widget _buildGameplaySection() {
    return _buildSection(
      'Gameplay Settings',
      Icons.gamepad,
      [
        _buildInfoTile(
          'Controls',
          'Tap left/right to move, swipe up to jump, swipe down to slide',
          Icons.touch_app,
        ),
        _buildInfoTile(
          'Difficulty',
          'Game speed increases automatically as you progress',
          Icons.trending_up,
        ),
      ],
    );
  }

  /// Build about section
  Widget _buildAboutSection() {
    return _buildSection(
      'About',
      Icons.info,
      [
        _buildInfoTile(
          'Version',
          '1.0.0',
          Icons.code,
        ),
        _buildInfoTile(
          'Developer',
          'Metro Dash Team',
          Icons.person,
        ),
        _buildInfoTile(
          'Engine',
          'Flutter + Flame',
          Icons.flutter_dash,
        ),
      ],
    );
  }

  /// Build section container
  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: GameConstants.primaryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: GameConstants.primaryColor, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    color: GameConstants.textColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// Build switch tile
  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: SwitchListTile(
        title: Text(
          title,
          style: const TextStyle(
            color: GameConstants.textColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: GameConstants.textColor.withValues(alpha: 0.7),
            fontSize: 14,
          ),
        ),
        secondary: Icon(icon, color: GameConstants.primaryColor),
        value: value,
        onChanged: onChanged,
        activeColor: GameConstants.primaryColor,
      ),
    );
  }

  /// Build info tile
  Widget _buildInfoTile(String title, String subtitle, IconData icon) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: ListTile(
        leading: Icon(icon, color: GameConstants.primaryColor),
        title: Text(
          title,
          style: const TextStyle(
            color: GameConstants.textColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: GameConstants.textColor.withValues(alpha: 0.7),
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  /// Toggle sound effects
  void _toggleSound(bool value) async {
    setState(() {
      playerData.soundEnabled = value;
    });
    
    await AudioService.instance.setSoundEnabled(value);
    await StorageService.instance.savePlayerData(playerData);
    
    if (value) {
      AudioService.instance.playButtonClickSound();
    }
  }

  /// Toggle background music
  void _toggleMusic(bool value) async {
    setState(() {
      playerData.musicEnabled = value;
    });
    
    await AudioService.instance.setMusicEnabled(value);
    await StorageService.instance.savePlayerData(playerData);
  }
}
