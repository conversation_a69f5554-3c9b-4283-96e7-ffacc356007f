import 'package:flutter/material.dart';

import '../models/achievement.dart';
import '../models/player_data.dart';
import '../utils/constants.dart';

import '../services/audio_service.dart';

/// Achievements screen
class AchievementsScreen extends StatefulWidget {
  final PlayerData playerData;

  const AchievementsScreen({super.key, required this.playerData});

  @override
  State<AchievementsScreen> createState() => _AchievementsScreenState();
}

class _AchievementsScreenState extends State<AchievementsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late List<Achievement> achievements;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAchievements();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Load achievements and update progress
  void _loadAchievements() {
    achievements = AchievementManager.getAllAchievements();
    _updateAchievementProgress();
  }

  /// Update achievement progress based on player data
  void _updateAchievementProgress() {
    for (final achievement in achievements) {
      switch (achievement.type) {
        case AchievementType.score:
          achievement.updateProgress(widget.playerData.highScore);
          break;
        case AchievementType.distance:
          achievement.updateProgress(widget.playerData.totalDistance.floor());
          break;
        case AchievementType.coins:
          achievement.updateProgress(widget.playerData.totalCoins);
          break;
        case AchievementType.games:
          achievement.updateProgress(widget.playerData.gamesPlayed);
          break;
        case AchievementType.characters:
          achievement.updateProgress(
            widget.playerData.unlockedCharacters.length,
          );
          break;
        case AchievementType.powerUps:
          // Count different power-ups used (simplified)
          achievement.updateProgress(widget.playerData.powerUpLevels.length);
          break;
        case AchievementType.special:
          // Handle special achievements
          if (achievement.id == 'first_run' &&
              widget.playerData.gamesPlayed > 0) {
            achievement.updateProgress(1);
          }
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: GameConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Achievements',
          style: TextStyle(
            color: GameConstants.textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: GameConstants.textColor),
          onPressed: () {
            AudioService.instance.playButtonClickSound();
            Navigator.pop(context);
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF1A1A2E), Color(0xFF16213E), Color(0xFF0F3460)],
          ),
        ),
        child: Column(
          children: [
            _buildProgressHeader(),
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAllAchievements(),
                  _buildUnlockedAchievements(),
                  _buildLockedAchievements(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build progress header
  Widget _buildProgressHeader() {
    final completionPercentage = AchievementManager.getCompletionPercentage(
      achievements,
    );
    final unlockedCount = AchievementManager.getUnlockedAchievements(
      achievements,
    ).length;
    final totalCount = achievements.length;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: GameConstants.primaryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            'Overall Progress',
            style: TextStyle(
              color: GameConstants.textColor.withValues(alpha: 0.8),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),

          // Progress circle
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: 80,
                height: 80,
                child: CircularProgressIndicator(
                  value: completionPercentage,
                  strokeWidth: 6,
                  backgroundColor: Colors.grey.withValues(alpha: 0.3),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    GameConstants.primaryColor,
                  ),
                ),
              ),
              Text(
                '${(completionPercentage * 100).round()}%',
                style: const TextStyle(
                  color: GameConstants.textColor,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),
          Text(
            '$unlockedCount / $totalCount Achievements',
            style: const TextStyle(
              color: GameConstants.textColor,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build tab bar
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(25),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: GameConstants.primaryColor,
          borderRadius: BorderRadius.circular(25),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: GameConstants.textColor.withValues(alpha: 0.7),
        tabs: const [
          Tab(text: 'All'),
          Tab(text: 'Unlocked'),
          Tab(text: 'Locked'),
        ],
      ),
    );
  }

  /// Build all achievements tab
  Widget _buildAllAchievements() {
    return _buildAchievementsList(achievements);
  }

  /// Build unlocked achievements tab
  Widget _buildUnlockedAchievements() {
    final unlockedAchievements = AchievementManager.getUnlockedAchievements(
      achievements,
    );
    return _buildAchievementsList(unlockedAchievements);
  }

  /// Build locked achievements tab
  Widget _buildLockedAchievements() {
    final lockedAchievements = AchievementManager.getLockedAchievements(
      achievements,
    );
    return _buildAchievementsList(lockedAchievements);
  }

  /// Build achievements list
  Widget _buildAchievementsList(List<Achievement> achievementList) {
    if (achievementList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events,
              size: 64,
              color: GameConstants.textColor.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No achievements here yet!',
              style: TextStyle(
                color: GameConstants.textColor.withValues(alpha: 0.6),
                fontSize: 18,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: achievementList.length,
      itemBuilder: (context, index) {
        final achievement = achievementList[index];
        return _buildAchievementCard(achievement);
      },
    );
  }

  /// Build achievement card
  Widget _buildAchievementCard(Achievement achievement) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(
          alpha: achievement.isUnlocked ? 0.15 : 0.05,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: achievement.isUnlocked
              ? achievement.color.withValues(alpha: 0.5)
              : Colors.grey.withValues(alpha: 0.2),
          width: achievement.isUnlocked ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          // Achievement icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: achievement.isUnlocked
                  ? achievement.color
                  : Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(achievement.icon, color: Colors.white, size: 30),
          ),

          const SizedBox(width: 16),

          // Achievement info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.name,
                  style: TextStyle(
                    color: achievement.isUnlocked
                        ? GameConstants.textColor
                        : GameConstants.textColor.withValues(alpha: 0.6),
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  achievement.description,
                  style: TextStyle(
                    color: GameConstants.textColor.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),

                // Progress bar
                if (!achievement.isUnlocked) ...[
                  LinearProgressIndicator(
                    value: achievement.progressPercentage,
                    backgroundColor: Colors.grey.withValues(alpha: 0.3),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      achievement.color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${achievement.currentProgress} / ${achievement.targetValue}',
                    style: TextStyle(
                      color: GameConstants.textColor.withValues(alpha: 0.6),
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Reward info
          Column(
            children: [
              if (achievement.isUnlocked)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'UNLOCKED',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              const SizedBox(height: 8),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.monetization_on,
                    color: GameConstants.coinColor,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${achievement.coinReward}',
                    style: const TextStyle(
                      color: GameConstants.coinColor,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
