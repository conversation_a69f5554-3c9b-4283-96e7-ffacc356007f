import 'package:flutter/material.dart';

import '../models/player_data.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../services/audio_service.dart';

/// Statistics screen showing player progress and stats
class StatisticsScreen extends StatefulWidget {
  final PlayerData playerData;

  const StatisticsScreen({
    super.key,
    required this.playerData,
  });

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Setup animations
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: GameConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Statistics',
          style: TextStyle(
            color: GameConstants.textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: GameConstants.textColor),
          onPressed: () {
            AudioService.instance.playButtonClickSound();
            Navigator.pop(context);
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildOverallStats(),
                const SizedBox(height: 20),
                _buildGameplayStats(),
                const SizedBox(height: 20),
                _buildProgressStats(),
                const SizedBox(height: 20),
                _buildTimeStats(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build overall statistics section
  Widget _buildOverallStats() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: GameConstants.primaryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Overall Statistics',
            style: TextStyle(
              color: GameConstants.textColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'High Score',
                  GameHelpers.formatScore(widget.playerData.highScore),
                  Icons.star,
                  GameConstants.secondaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'Total Coins',
                  '${widget.playerData.totalCoins}',
                  Icons.monetization_on,
                  GameConstants.coinColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build gameplay statistics section
  Widget _buildGameplayStats() {
    final averageScore = widget.playerData.gamesPlayed > 0
        ? (widget.playerData.highScore / widget.playerData.gamesPlayed).round()
        : 0;
    
    final averageDistance = widget.playerData.gamesPlayed > 0
        ? (widget.playerData.totalDistance / widget.playerData.gamesPlayed).round()
        : 0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: GameConstants.primaryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Gameplay Statistics',
            style: TextStyle(
              color: GameConstants.textColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildStatRow(
            'Games Played',
            '${widget.playerData.gamesPlayed}',
            Icons.play_arrow,
            GameConstants.primaryColor,
          ),
          const SizedBox(height: 12),
          
          _buildStatRow(
            'Total Distance',
            '${widget.playerData.totalDistance.floor()}m',
            Icons.straighten,
            Colors.green,
          ),
          const SizedBox(height: 12),
          
          _buildStatRow(
            'Average Score',
            GameHelpers.formatScore(averageScore),
            Icons.trending_up,
            Colors.orange,
          ),
          const SizedBox(height: 12),
          
          _buildStatRow(
            'Average Distance',
            '${averageDistance}m',
            Icons.timeline,
            Colors.blue,
          ),
        ],
      ),
    );
  }

  /// Build progress statistics section
  Widget _buildProgressStats() {
    final unlockedCharacters = widget.playerData.unlockedCharacters.length;
    final totalCharacters = 5; // Assuming 5 total characters
    final characterProgress = unlockedCharacters / totalCharacters;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: GameConstants.primaryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Progress Statistics',
            style: TextStyle(
              color: GameConstants.textColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildProgressItem(
            'Characters Unlocked',
            '$unlockedCharacters / $totalCharacters',
            characterProgress,
            Colors.purple,
          ),
          const SizedBox(height: 16),
          
          _buildProgressItem(
            'Power-ups Upgraded',
            '${widget.playerData.powerUpLevels.length} / 4',
            widget.playerData.powerUpLevels.length / 4,
            Colors.red,
          ),
        ],
      ),
    );
  }

  /// Build time statistics section
  Widget _buildTimeStats() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: GameConstants.primaryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Time Statistics',
            style: TextStyle(
              color: GameConstants.textColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildStatRow(
            'Total Play Time',
            GameHelpers.formatTime(widget.playerData.totalPlayTime),
            Icons.access_time,
            Colors.cyan,
          ),
          const SizedBox(height: 12),
          
          _buildStatRow(
            'Average Game Time',
            widget.playerData.gamesPlayed > 0
                ? GameHelpers.formatTime(Duration(
                    milliseconds: widget.playerData.totalPlayTime.inMilliseconds ~/ 
                        widget.playerData.gamesPlayed,
                  ))
                : '00:00',
            Icons.timer,
            Colors.teal,
          ),
        ],
      ),
    );
  }

  /// Build individual stat item
  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: GameConstants.textColor.withValues(alpha: 0.7),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build stat row
  Widget _buildStatRow(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              color: GameConstants.textColor.withValues(alpha: 0.8),
              fontSize: 16,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Build progress item with progress bar
  Widget _buildProgressItem(String label, String value, double progress, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                color: GameConstants.textColor.withValues(alpha: 0.8),
                fontSize: 16,
              ),
            ),
            Text(
              value,
              style: TextStyle(
                color: color,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: Colors.grey.withValues(alpha: 0.3),
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }
}
