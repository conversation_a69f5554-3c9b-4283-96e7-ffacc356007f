import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flame/effects.dart';
import 'package:flutter/material.dart';

import '../utils/constants.dart';
import '../utils/helpers.dart';

/// Coin component for Metro Dash
class Coin extends SpriteComponent with CollisionCallbacks {
  final TrackPosition trackPosition;
  final double gameSpeed;
  final CoinType coinType;

  // Movement and animation properties
  late double moveSpeed;
  bool isActive = true;
  bool isCollected = false;
  double rotationSpeed = 5.0;
  double bobOffset = 0.0;
  double bobSpeed = 3.0;
  double bobAmplitude = 5.0;

  // Visual effects
  late Timer sparkleTimer;
  bool showSparkle = false;

  Coin({
    required this.trackPosition,
    required this.gameSpeed,
    this.coinType = CoinType.normal,
  }) {
    moveSpeed = gameSpeed;
  }

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Set coin size
    size = Vector2(GameConstants.coinSize, GameConstants.coinSize);

    // Set position
    position = Vector2(
      trackPosition.xPosition - size.x / 2,
      -size.y, // Start above screen
    );

    // Set visual appearance
    _setCoinAppearance();

    // Add collision detection
    add(RectangleHitbox());

    // Setup sparkle effect timer
    sparkleTimer = Timer(
      0.5, // Sparkle every 0.5 seconds
      onTick: () => showSparkle = !showSparkle,
      repeat: true,
    );
    sparkleTimer.start();

    print('Coin created at track ${trackPosition.index}');
  }

  /// Set coin visual appearance
  void _setCoinAppearance() {
    switch (coinType) {
      case CoinType.normal:
        paint = Paint()..color = GameConstants.coinColor;
        break;
      case CoinType.silver:
        paint = Paint()..color = Colors.grey[300]!;
        break;
      case CoinType.diamond:
        paint = Paint()..color = Colors.cyan[300]!;
        break;
    }
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (!isActive || isCollected) return;

    // Update timers
    sparkleTimer.update(dt);

    // Move coin down
    position.y += moveSpeed * dt;

    // Rotate coin
    angle += rotationSpeed * dt;

    // Bob up and down
    bobOffset += bobSpeed * dt;
    final bobY = math.sin(bobOffset) * bobAmplitude;
    position.y += bobY * dt;

    // Update sparkle effect
    if (showSparkle) {
      _updateSparkleEffect(dt);
    }

    // Remove if off screen
    if (position.y > GameConstants.gameHeight + size.y) {
      _removeCoin();
    }
  }

  /// Update sparkle visual effect
  void _updateSparkleEffect(double dt) {
    // Create sparkle effect by slightly changing the color brightness
    final sparkleIntensity = (math.sin(bobOffset * 2) + 1) / 2;
    final baseColor = coinType.color;

    paint = Paint()
      ..color = Color.lerp(baseColor, Colors.white, sparkleIntensity * 0.3)!;
  }

  /// Collect the coin
  void collect() {
    if (isCollected) return;

    isCollected = true;
    isActive = false;

    // Play collection animation
    _playCollectionAnimation();
  }

  /// Play collection animation
  void _playCollectionAnimation() {
    // Scale up and fade out
    final scaleEffect = ScaleEffect.to(
      Vector2.all(1.5),
      EffectController(duration: 0.3),
    );

    final fadeEffect = OpacityEffect.to(0.0, EffectController(duration: 0.3));

    add(scaleEffect);
    add(fadeEffect);

    // Remove after animation
    Timer(0.3, onTick: () => _removeCoin());
  }

  /// Remove coin from game
  void _removeCoin() {
    isActive = false;
    removeFromParent();
  }

  /// Handle collision with player
  @override
  bool onCollision(Set<Vector2> intersectionPoints, PositionComponent other) {
    super.onCollision(intersectionPoints, other);

    if (other.runtimeType.toString() == 'Player' && isActive && !isCollected) {
      collect();
      _handlePlayerCollection();
    }

    return true;
  }

  /// Handle player collection
  void _handlePlayerCollection() {
    // This will be handled by the collision detector in the game
    print('Player collected coin!');
  }

  /// Get coin value based on type
  int get value => coinType.value;

  /// Get collision rectangle
  Rect get collisionRect {
    return Rect.fromLTWH(position.x, position.y, size.x, size.y);
  }

  /// Check if coin is visible on screen
  bool get isOnScreen {
    return position.y < GameConstants.gameHeight && position.y + size.y > 0;
  }

  /// Get coin center position
  Vector2 get centerPosition => position + size / 2;

  /// Update movement speed
  void updateSpeed(double newSpeed) {
    moveSpeed = newSpeed;
  }

  /// Create floating text effect for coin value
  void createValueText() {
    final valueText = TextComponent(
      text: '+${coinType.value}',
      position: centerPosition,
      textRenderer: TextPaint(
        style: const TextStyle(
          color: GameConstants.coinColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );

    // Add floating animation
    final moveEffect = MoveEffect.to(
      centerPosition + Vector2(0, -50),
      EffectController(duration: 1.0),
    );

    final fadeEffect = OpacityEffect.to(0.0, EffectController(duration: 1.0));

    valueText.add(moveEffect);
    valueText.add(fadeEffect);

    parent?.add(valueText);

    // Remove after animation
    Timer(1.0, onTick: () => valueText.removeFromParent());
  }
}

/// Types of coins
enum CoinType { normal, silver, diamond }

/// Extension for coin type properties
extension CoinTypeExtension on CoinType {
  String get name {
    switch (this) {
      case CoinType.normal:
        return 'Gold Coin';
      case CoinType.silver:
        return 'Silver Coin';
      case CoinType.diamond:
        return 'Diamond';
    }
  }

  Color get color {
    switch (this) {
      case CoinType.normal:
        return GameConstants.coinColor;
      case CoinType.silver:
        return Colors.grey[300]!;
      case CoinType.diamond:
        return Colors.cyan[300]!;
    }
  }

  int get value {
    switch (this) {
      case CoinType.normal:
        return GameConstants.coinValue;
      case CoinType.silver:
        return GameConstants.coinValue * 2;
      case CoinType.diamond:
        return GameConstants.coinValue * 5;
    }
  }

  double get rarity {
    switch (this) {
      case CoinType.normal:
        return 0.7; // 70% chance
      case CoinType.silver:
        return 0.25; // 25% chance
      case CoinType.diamond:
        return 0.05; // 5% chance
    }
  }

  /// Get random coin type based on rarity
  static CoinType getRandom() {
    final random = GameHelpers.randomDouble(0.0, 1.0);

    if (random < CoinType.diamond.rarity) {
      return CoinType.diamond;
    } else if (random < CoinType.diamond.rarity + CoinType.silver.rarity) {
      return CoinType.silver;
    } else {
      return CoinType.normal;
    }
  }
}
