/// Player data model for Metro Dash
class PlayerData {
  int highScore;
  int totalCoins;
  bool soundEnabled;
  bool musicEnabled;
  String selectedCharacter;
  List<String> unlockedCharacters;
  Map<String, int> powerUpLevels;
  int gamesPlayed;
  double totalDistance;
  Duration totalPlayTime;
  Map<String, bool> achievements;

  PlayerData({
    this.highScore = 0,
    this.totalCoins = 0,
    this.soundEnabled = true,
    this.musicEnabled = true,
    this.selectedCharacter = 'default',
    List<String>? unlockedCharacters,
    Map<String, int>? powerUpLevels,
    this.gamesPlayed = 0,
    this.totalDistance = 0.0,
    this.totalPlayTime = Duration.zero,
    Map<String, bool>? achievements,
  }) : unlockedCharacters = unlockedCharacters ?? ['default'],
       powerUpLevels = powerUpLevels ?? _getDefaultPowerUpLevels(),
       achievements = achievements ?? _getDefaultAchievements();

  /// Default power-up levels
  static Map<String, int> _getDefaultPowerUpLevels() {
    return {'magnet': 1, 'shield': 1, 'multiplier': 1, 'jetpack': 1};
  }

  /// Default achievements
  static Map<String, bool> _getDefaultAchievements() {
    return {
      'first_run': false,
      'collect_100_coins': false,
      'collect_1000_coins': false,
      'score_1000': false,
      'score_10000': false,
      'run_1000m': false,
      'run_5000m': false,
      'play_10_games': false,
      'play_100_games': false,
      'use_all_powerups': false,
      'unlock_all_characters': false,
    };
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'highScore': highScore,
      'totalCoins': totalCoins,
      'soundEnabled': soundEnabled,
      'musicEnabled': musicEnabled,
      'selectedCharacter': selectedCharacter,
      'unlockedCharacters': unlockedCharacters,
      'powerUpLevels': powerUpLevels,
      'gamesPlayed': gamesPlayed,
      'totalDistance': totalDistance,
      'totalPlayTime': totalPlayTime.inMilliseconds,
      'achievements': achievements,
    };
  }

  /// Create from JSON
  factory PlayerData.fromJson(Map<String, dynamic> json) {
    return PlayerData(
      highScore: json['highScore'] ?? 0,
      totalCoins: json['totalCoins'] ?? 0,
      soundEnabled: json['soundEnabled'] ?? true,
      musicEnabled: json['musicEnabled'] ?? true,
      selectedCharacter: json['selectedCharacter'] ?? 'default',
      unlockedCharacters: List<String>.from(
        json['unlockedCharacters'] ?? ['default'],
      ),
      powerUpLevels: Map<String, int>.from(
        json['powerUpLevels'] ?? _getDefaultPowerUpLevels(),
      ),
      gamesPlayed: json['gamesPlayed'] ?? 0,
      totalDistance: json['totalDistance']?.toDouble() ?? 0.0,
      totalPlayTime: Duration(milliseconds: json['totalPlayTime'] ?? 0),
      achievements: Map<String, bool>.from(
        json['achievements'] ?? _getDefaultAchievements(),
      ),
    );
  }

  /// Update high score if new score is higher
  bool updateHighScore(int newScore) {
    if (newScore > highScore) {
      highScore = newScore;
      return true;
    }
    return false;
  }

  /// Add coins to total
  void addCoins(int coins) {
    totalCoins += coins;
    _checkCoinAchievements();
  }

  /// Spend coins (returns true if successful)
  bool spendCoins(int coins) {
    if (totalCoins >= coins) {
      totalCoins -= coins;
      return true;
    }
    return false;
  }

  /// Unlock a character
  void unlockCharacter(String characterId) {
    if (!unlockedCharacters.contains(characterId)) {
      unlockedCharacters.add(characterId);
      _checkCharacterAchievements();
    }
  }

  /// Check if character is unlocked
  bool isCharacterUnlocked(String characterId) {
    return unlockedCharacters.contains(characterId);
  }

  /// Upgrade power-up level
  bool upgradePowerUp(String powerUpType, int cost) {
    if (spendCoins(cost)) {
      powerUpLevels[powerUpType] = (powerUpLevels[powerUpType] ?? 1) + 1;
      return true;
    }
    return false;
  }

  /// Get power-up level
  int getPowerUpLevel(String powerUpType) {
    return powerUpLevels[powerUpType] ?? 1;
  }

  /// Record game completion
  void recordGameCompletion(int score, double distance, Duration playTime) {
    gamesPlayed++;
    totalDistance += distance;
    totalPlayTime += playTime;
    updateHighScore(score);
    _checkGameAchievements();
  }

  /// Check and unlock achievements
  void _checkCoinAchievements() {
    if (totalCoins >= 100 && !achievements['collect_100_coins']!) {
      achievements['collect_100_coins'] = true;
    }
    if (totalCoins >= 1000 && !achievements['collect_1000_coins']!) {
      achievements['collect_1000_coins'] = true;
    }
  }

  void _checkGameAchievements() {
    if (gamesPlayed >= 10 && !achievements['play_10_games']!) {
      achievements['play_10_games'] = true;
    }
    if (gamesPlayed >= 100 && !achievements['play_100_games']!) {
      achievements['play_100_games'] = true;
    }
    if (highScore >= 1000 && !achievements['score_1000']!) {
      achievements['score_1000'] = true;
    }
    if (highScore >= 10000 && !achievements['score_10000']!) {
      achievements['score_10000'] = true;
    }
    if (totalDistance >= 1000 && !achievements['run_1000m']!) {
      achievements['run_1000m'] = true;
    }
    if (totalDistance >= 5000 && !achievements['run_5000m']!) {
      achievements['run_5000m'] = true;
    }
  }

  void _checkCharacterAchievements() {
    // Assuming we have 5 total characters
    if (unlockedCharacters.length >= 5 &&
        !achievements['unlock_all_characters']!) {
      achievements['unlock_all_characters'] = true;
    }
  }

  /// Get achievement progress
  double getAchievementProgress() {
    final unlockedCount = achievements.values
        .where((unlocked) => unlocked)
        .length;
    return unlockedCount / achievements.length;
  }

  /// Get newly unlocked achievements
  List<String> getNewlyUnlockedAchievements(PlayerData previousData) {
    final newAchievements = <String>[];
    achievements.forEach((key, value) {
      if (value && !previousData.achievements[key]!) {
        newAchievements.add(key);
      }
    });
    return newAchievements;
  }

  /// Copy with new values
  PlayerData copyWith({
    int? highScore,
    int? totalCoins,
    bool? soundEnabled,
    bool? musicEnabled,
    String? selectedCharacter,
    List<String>? unlockedCharacters,
    Map<String, int>? powerUpLevels,
    int? gamesPlayed,
    double? totalDistance,
    Duration? totalPlayTime,
    Map<String, bool>? achievements,
  }) {
    return PlayerData(
      highScore: highScore ?? this.highScore,
      totalCoins: totalCoins ?? this.totalCoins,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      musicEnabled: musicEnabled ?? this.musicEnabled,
      selectedCharacter: selectedCharacter ?? this.selectedCharacter,
      unlockedCharacters:
          unlockedCharacters ?? List.from(this.unlockedCharacters),
      powerUpLevels: powerUpLevels ?? Map.from(this.powerUpLevels),
      gamesPlayed: gamesPlayed ?? this.gamesPlayed,
      totalDistance: totalDistance ?? this.totalDistance,
      totalPlayTime: totalPlayTime ?? this.totalPlayTime,
      achievements: achievements ?? Map.from(this.achievements),
    );
  }
}
