# Metro Dash - Endless Runner Game

🏃‍♂️ **Metro Dash** is an endless runner game inspired by Subway Surfers, built with Flutter and Flame engine.

## 🎮 Game Features

### Core Gameplay
- **Endless Running**: Run as far as you can while avoiding obstacles
- **3-Track System**: Move between left, center, and right tracks
- **Player Actions**: Jump, slide, move left/right
- **Dynamic Difficulty**: Game speed increases over time
- **Collision Detection**: Advanced collision system with different obstacle types

### Game Elements
- **🚧 Obstacles**: Barriers, trains, signs, and boxes with different behaviors
- **💰 Coins**: Gold, silver, and diamond coins with different values
- **⚡ Power-ups**: Magnet, shield, score multiplier, and jetpack
- **🏃‍♂️ Characters**: Multiple unlockable characters with unique abilities
- **🎵 Audio**: Sound effects and background music

### User Interface
- **🏠 Home Screen**: Game stats, high score, and navigation
- **🎮 Game Screen**: Real-time UI with score, distance, and coins
- **🛒 Shop**: Purchase characters and upgrade power-ups
- **🏆 Achievements**: Track progress and unlock rewards
- **📊 Statistics**: Detailed gameplay statistics
- **⚙️ Settings**: Audio controls and game preferences

## 🛠️ Technical Stack

- **Framework**: Flutter 3.x
- **Game Engine**: Flame 1.15.0
- **Audio**: flame_audio + audioplayers
- **Storage**: shared_preferences
- **State Management**: Provider
- **Platform**: Android, Web, Windows

## 🚀 Getting Started

### Prerequisites
- Flutter SDK 3.0 or higher
- Dart SDK 3.0 or higher
- Android Studio / VS Code
- Chrome browser (for web testing)

### Installation

1. **Install dependencies**
   ```bash
   flutter pub get
   ```

2. **Run the game**
   ```bash
   # For Android
   flutter run

   # For Web
   flutter run -d chrome

   # For Windows
   flutter run -d windows
   ```

### Building for Production

```bash
# Android APK
flutter build apk --release

# Web
flutter build web --release

# Windows
flutter build windows --release
```

## 🎯 Game Controls

### Touch Controls
- **Tap Left Side**: Move left
- **Tap Right Side**: Move right
- **Tap Upper Middle**: Jump
- **Tap Lower Middle**: Slide
- **Swipe Left/Right**: Move between tracks
- **Swipe Up**: Jump
- **Swipe Down**: Slide

### Keyboard Controls (Desktop)
- **A/Left Arrow**: Move left
- **D/Right Arrow**: Move right
- **W/Up Arrow/Space**: Jump
- **S/Down Arrow**: Slide

## 🏆 Game Mechanics

### Scoring System
- **Distance**: Points for distance traveled
- **Coins**: Bonus points for coins collected
- **Multipliers**: Power-ups can multiply score
- **Survival**: Longer runs = higher scores

### Power-ups
- **🧲 Magnet**: Attracts nearby coins
- **🛡️ Shield**: Protects from one collision
- **✖️ Multiplier**: Doubles score for limited time
- **🚀 Jetpack**: Fly over obstacles

## 📱 Platform Support

- ✅ **Android**: Full support with touch controls
- ✅ **Web**: Runs in Chrome with touch/mouse controls
- ✅ **Windows**: Desktop support with keyboard controls
- 🔄 **iOS**: Ready for iOS deployment (requires Xcode)

---

**Made with ❤️ using Flutter & Flame**
