import 'package:flutter/material.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame_audio/flame_audio.dart';

void main() {
  runApp(const MetroDashApp());
}

class MetroDashApp extends StatelessWidget {
  const MetroDashApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Metro Dash',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const GameScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  late MetroDashGame game;

  @override
  void initState() {
    super.initState();
    game = MetroDashGame();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: Stack(
          children: [
            GameWidget(game: game),
            Positioned(
              top: 50,
              left: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Score: ${game.score}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Distance: ${game.distance.toInt()}m',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      'Coins: ${game.coins}',
                      style: const TextStyle(
                        color: Colors.yellow,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 50,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildControlButton('←', () => game.moveLeft()),
                  _buildControlButton('↑', () => game.jump()),
                  _buildControlButton('↓', () => game.slide()),
                  _buildControlButton('→', () => game.moveRight()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton(String label, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue.withValues(alpha: 0.8),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.all(20),
        shape: const CircleBorder(),
      ),
      child: Text(
        label,
        style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
      ),
    );
  }
}

class MetroDashGame extends FlameGame with HasKeyboardHandlerComponents, HasCollisionDetection {
  int score = 0;
  double distance = 0;
  int coins = 0;
  late Player player;
  double gameSpeed = 200;
  double spawnTimer = 0;
  final double spawnInterval = 2.0;

  @override
  Future<void> onLoad() async {
    // Add player
    player = Player();
    add(player);

    // Add ground
    add(Ground());

    // Start spawning obstacles and coins
    _startSpawning();
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Update distance and score
    distance += gameSpeed * dt / 10;
    score = distance.toInt();
    
    // Increase game speed over time
    gameSpeed += dt * 5;
    
    // Spawn obstacles and coins
    spawnTimer += dt;
    if (spawnTimer >= spawnInterval) {
      _spawnRandomItem();
      spawnTimer = 0;
    }
  }

  void _startSpawning() {
    // Initial spawn
    _spawnRandomItem();
  }

  void _spawnRandomItem() {
    final random = DateTime.now().millisecondsSinceEpoch % 100;
    
    if (random < 30) {
      // Spawn coin
      add(Coin());
    } else {
      // Spawn obstacle
      add(Obstacle());
    }
  }

  void moveLeft() => player.moveLeft();
  void moveRight() => player.moveRight();
  void jump() => player.jump();
  void slide() => player.slide();

  void collectCoin() {
    coins++;
    score += 10;
  }

  void gameOver() {
    // Handle game over
    pauseEngine();
  }
}

class Player extends RectangleComponent with HasGameRef<MetroDashGame> {
  int currentLane = 1; // 0=left, 1=center, 2=right
  bool isJumping = false;
  bool isSliding = false;
  double jumpHeight = 0;
  final double laneWidth = 100;
  final double playerWidth = 40;
  final double playerHeight = 60;

  @override
  Future<void> onLoad() async {
    size = Vector2(playerWidth, playerHeight);
    position = Vector2(
      gameRef.size.x / 2 - playerWidth / 2,
      gameRef.size.y - 150,
    );
    paint = Paint()..color = Colors.blue;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Update lane position
    final targetX = (gameRef.size.x / 2 - playerWidth / 2) + (currentLane - 1) * laneWidth;
    position.x += (targetX - position.x) * dt * 5;
    
    // Handle jumping
    if (isJumping) {
      jumpHeight += dt * 300;
      if (jumpHeight >= 100) {
        isJumping = false;
      }
    } else if (jumpHeight > 0) {
      jumpHeight -= dt * 300;
      if (jumpHeight <= 0) {
        jumpHeight = 0;
      }
    }
    
    // Handle sliding
    if (isSliding) {
      size.y = playerHeight / 2;
    } else {
      size.y = playerHeight;
    }
    
    // Update Y position based on jump and slide
    position.y = gameRef.size.y - 150 - jumpHeight + (isSliding ? playerHeight / 2 : 0);
  }

  void moveLeft() {
    if (currentLane > 0) currentLane--;
  }

  void moveRight() {
    if (currentLane < 2) currentLane++;
  }

  void jump() {
    if (!isJumping && jumpHeight == 0) {
      isJumping = true;
      jumpHeight = 1;
    }
  }

  void slide() {
    isSliding = true;
    Future.delayed(const Duration(milliseconds: 500), () {
      isSliding = false;
    });
  }
}

class Ground extends RectangleComponent {
  @override
  Future<void> onLoad() async {
    size = Vector2(gameRef.size.x, 50);
    position = Vector2(0, gameRef.size.y - 50);
    paint = Paint()..color = Colors.grey;
  }
}

class Obstacle extends RectangleComponent with HasGameRef<MetroDashGame> {
  @override
  Future<void> onLoad() async {
    final lane = DateTime.now().millisecondsSinceEpoch % 3;
    size = Vector2(40, 60);
    position = Vector2(
      (gameRef.size.x / 2 - 20) + (lane - 1) * 100,
      -60,
    );
    paint = Paint()..color = Colors.red;
  }

  @override
  void update(double dt) {
    super.update(dt);
    position.y += gameRef.gameSpeed * dt;
    
    if (position.y > gameRef.size.y) {
      removeFromParent();
    }
    
    // Check collision with player
    if (toRect().overlaps(gameRef.player.toRect())) {
      gameRef.gameOver();
    }
  }
}

class Coin extends CircleComponent with HasGameRef<MetroDashGame> {
  @override
  Future<void> onLoad() async {
    final lane = DateTime.now().millisecondsSinceEpoch % 3;
    radius = 15;
    position = Vector2(
      (gameRef.size.x / 2 - radius) + (lane - 1) * 100,
      -30,
    );
    paint = Paint()..color = Colors.yellow;
  }

  @override
  void update(double dt) {
    super.update(dt);
    position.y += gameRef.gameSpeed * dt;
    
    if (position.y > gameRef.size.y) {
      removeFromParent();
    }
    
    // Check collision with player
    if (toRect().overlaps(gameRef.player.toRect())) {
      gameRef.collectCoin();
      removeFromParent();
    }
  }
}
