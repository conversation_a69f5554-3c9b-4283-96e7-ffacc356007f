# Audio Assets for Metro Dash

This directory contains all audio files used in the Metro Dash game.

## Required Audio Files:

### Sound Effects:
- `jump.wav` - Player jump sound effect
- `coin.wav` - Coin collection sound effect  
- `crash.wav` - Collision/crash sound effect
- `powerup.wav` - Power-up collection sound effect
- `button_click.wav` - UI button click sound effect
- `whoosh.wav` - Movement/swipe sound effect

### Background Music:
- `background_music.mp3` - Main background music for the game

## Audio Specifications:
- Format: WAV for sound effects, MP3 for music
- Sample Rate: 44.1 kHz recommended
- Bit Depth: 16-bit recommended
- Duration: Sound effects should be 0.5-2 seconds, music can be 2-5 minutes (looped)

## Usage:
These audio files are loaded and played by the AudioService class in the game.
Make sure all files are present for the audio system to work properly.

## Licensing:
Ensure all audio files are properly licensed for use in your game.
Consider using royalty-free audio from sources like:
- Freesound.org
- Zapsplat.com
- Adobe Stock Audio
- Or create your own audio assets

## Note:
The current files are placeholders. Replace them with actual audio content for the final game.
