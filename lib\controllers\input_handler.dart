import 'package:flutter/material.dart';
import 'package:flame/components.dart';
import 'package:flame/events.dart';

import '../game.dart';
import '../components/player.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../services/audio_service.dart';

/// Input handler for touch controls
class InputHandler extends Component
    with HasGameReference<MetroDashGame>, TapCallbacks {
  late Player player;

  // Touch zones
  late Rect leftZone;
  late Rect rightZone;
  late Rect jumpZone;
  late Rect slideZone;

  // Swipe detection
  Vector2? swipeStart;
  static const double swipeThreshold = 50.0;
  static const double swipeTimeThreshold = 500.0; // milliseconds
  DateTime? swipeStartTime;

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    player = game.player;
    _setupTouchZones();
  }

  /// Setup touch zones for different actions
  void _setupTouchZones() {
    final gameWidth = GameConstants.gameWidth;
    final gameHeight = GameConstants.gameHeight;

    // Divide screen into zones
    // Left third for left movement
    leftZone = Rect.fromLTWH(0, 0, gameWidth / 3, gameHeight);

    // Right third for right movement
    rightZone = Rect.fromLTWH(gameWidth * 2 / 3, 0, gameWidth / 3, gameHeight);

    // Upper middle for jump
    jumpZone = Rect.fromLTWH(gameWidth / 3, 0, gameWidth / 3, gameHeight / 2);

    // Lower middle for slide
    slideZone = Rect.fromLTWH(
      gameWidth / 3,
      gameHeight / 2,
      gameWidth / 3,
      gameHeight / 2,
    );
  }

  @override
  bool onTapDown(TapDownEvent event) {
    final tapPosition = event.localPosition;

    // Check which zone was tapped
    if (leftZone.contains(tapPosition.toOffset())) {
      _handleLeftTap();
    } else if (rightZone.contains(tapPosition.toOffset())) {
      _handleRightTap();
    } else if (jumpZone.contains(tapPosition.toOffset())) {
      _handleJumpTap();
    } else if (slideZone.contains(tapPosition.toOffset())) {
      _handleSlideTap();
    }

    // Start swipe detection
    swipeStart = tapPosition;
    swipeStartTime = DateTime.now();

    return true;
  }

  @override
  bool onTapUp(TapUpEvent event) {
    final tapPosition = event.localPosition;

    // Check for swipe gestures
    if (swipeStart != null && swipeStartTime != null) {
      _handleSwipeGesture(swipeStart!, tapPosition);
    }

    // Reset swipe detection
    swipeStart = null;
    swipeStartTime = null;

    return true;
  }

  /// Handle left movement tap
  void _handleLeftTap() {
    if (game.gameState == GameConstants.gameStatePlaying) {
      player.moveLeft();
      AudioService.instance.playWhooshSound();
      GameHelpers.lightVibrate();
    }
  }

  /// Handle right movement tap
  void _handleRightTap() {
    if (game.gameState == GameConstants.gameStatePlaying) {
      player.moveRight();
      AudioService.instance.playWhooshSound();
      GameHelpers.lightVibrate();
    }
  }

  /// Handle jump tap
  void _handleJumpTap() {
    if (game.gameState == GameConstants.gameStatePlaying) {
      player.jump();
      AudioService.instance.playJumpSound();
      GameHelpers.lightVibrate();
    }
  }

  /// Handle slide tap
  void _handleSlideTap() {
    if (game.gameState == GameConstants.gameStatePlaying) {
      player.slide();
      AudioService.instance.playWhooshSound();
      GameHelpers.lightVibrate();
    }
  }

  /// Handle swipe gestures
  void _handleSwipeGesture(Vector2 start, Vector2 end) {
    if (swipeStartTime == null) return;

    final swipeDuration = DateTime.now()
        .difference(swipeStartTime!)
        .inMilliseconds;
    if (swipeDuration > swipeTimeThreshold) return;

    final swipeVector = end - start;
    final swipeDistance = swipeVector.length;

    if (swipeDistance < swipeThreshold) return;

    // Determine swipe direction
    final angle = swipeVector.angleToSigned(Vector2(1, 0));
    final absAngle = angle.abs();

    if (game.gameState != GameConstants.gameStatePlaying) return;

    // Horizontal swipes
    if (absAngle < 0.785) {
      // ~45 degrees
      if (angle > 0) {
        // Swipe right
        player.moveRight();
        AudioService.instance.playWhooshSound();
      } else {
        // Swipe left
        player.moveLeft();
        AudioService.instance.playWhooshSound();
      }
    }
    // Vertical swipes
    else if (absAngle > 2.356) {
      // ~135 degrees
      if (swipeVector.y < 0) {
        // Swipe up - jump
        player.jump();
        AudioService.instance.playJumpSound();
      } else {
        // Swipe down - slide
        player.slide();
        AudioService.instance.playWhooshSound();
      }
    }

    GameHelpers.lightVibrate();
  }

  /// Get touch zone for debugging
  String getTouchZone(Vector2 position) {
    if (leftZone.contains(position.toOffset())) {
      return 'Left';
    } else if (rightZone.contains(position.toOffset())) {
      return 'Right';
    } else if (jumpZone.contains(position.toOffset())) {
      return 'Jump';
    } else if (slideZone.contains(position.toOffset())) {
      return 'Slide';
    }
    return 'None';
  }

  /// Draw touch zones for debugging (optional)
  void drawTouchZones(Canvas canvas) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // Draw left zone
    paint.color = Colors.red.withValues(alpha: 0.3);
    canvas.drawRect(leftZone, paint);

    // Draw right zone
    paint.color = Colors.blue.withValues(alpha: 0.3);
    canvas.drawRect(rightZone, paint);

    // Draw jump zone
    paint.color = Colors.green.withValues(alpha: 0.3);
    canvas.drawRect(jumpZone, paint);

    // Draw slide zone
    paint.color = Colors.yellow.withValues(alpha: 0.3);
    canvas.drawRect(slideZone, paint);
  }
}

/// Touch control overlay widget
class TouchControlOverlay extends StatelessWidget {
  final VoidCallback? onLeftTap;
  final VoidCallback? onRightTap;
  final VoidCallback? onJumpTap;
  final VoidCallback? onSlideTap;
  final bool showControls;

  const TouchControlOverlay({
    super.key,
    this.onLeftTap,
    this.onRightTap,
    this.onJumpTap,
    this.onSlideTap,
    this.showControls = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!showControls) return const SizedBox.shrink();

    return Positioned.fill(
      child: Row(
        children: [
          // Left control
          Expanded(
            child: GestureDetector(
              onTap: onLeftTap,
              child: Container(
                color: Colors.transparent,
                child: const Center(
                  child: Icon(
                    Icons.arrow_left,
                    color: Colors.white24,
                    size: 48,
                  ),
                ),
              ),
            ),
          ),

          // Middle controls (jump/slide)
          Expanded(
            child: Column(
              children: [
                // Jump area
                Expanded(
                  child: GestureDetector(
                    onTap: onJumpTap,
                    child: Container(
                      color: Colors.transparent,
                      child: const Center(
                        child: Icon(
                          Icons.keyboard_arrow_up,
                          color: Colors.white24,
                          size: 48,
                        ),
                      ),
                    ),
                  ),
                ),

                // Slide area
                Expanded(
                  child: GestureDetector(
                    onTap: onSlideTap,
                    child: Container(
                      color: Colors.transparent,
                      child: const Center(
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          color: Colors.white24,
                          size: 48,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Right control
          Expanded(
            child: GestureDetector(
              onTap: onRightTap,
              child: Container(
                color: Colors.transparent,
                child: const Center(
                  child: Icon(
                    Icons.arrow_right,
                    color: Colors.white24,
                    size: 48,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
