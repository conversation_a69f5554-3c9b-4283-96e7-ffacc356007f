import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';

import '../utils/constants.dart';
import '../utils/helpers.dart';

/// Obstacle component for Metro Dash
class Obstacle extends SpriteComponent with CollisionCallbacks {
  final TrackPosition trackPosition;
  final double gameSpeed;
  final ObstacleType obstacleType;
  
  // Movement properties
  late double moveSpeed;
  bool isActive = true;
  
  Obstacle({
    required this.trackPosition,
    required this.gameSpeed,
    this.obstacleType = ObstacleType.barrier,
  }) {
    moveSpeed = gameSpeed;
  }
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    // Set obstacle size based on type
    _setObstacleSize();
    
    // Set position
    position = Vector2(
      trackPosition.xPosition - size.x / 2,
      -size.y, // Start above screen
    );
    
    // Set visual appearance based on type
    _setObstacleAppearance();
    
    // Add collision detection
    add(RectangleHitbox());
    
    print('Obstacle created at track ${trackPosition.index}');
  }
  
  /// Set obstacle size based on type
  void _setObstacleSize() {
    switch (obstacleType) {
      case ObstacleType.barrier:
        size = Vector2(GameConstants.obstacleWidth, GameConstants.obstacleHeight);
        break;
      case ObstacleType.train:
        size = Vector2(GameConstants.obstacleWidth * 1.5, GameConstants.obstacleHeight * 1.2);
        break;
      case ObstacleType.sign:
        size = Vector2(GameConstants.obstacleWidth * 0.8, GameConstants.obstacleHeight * 0.6);
        break;
      case ObstacleType.box:
        size = Vector2(GameConstants.obstacleWidth * 0.7, GameConstants.obstacleWidth * 0.7);
        break;
    }
  }
  
  /// Set obstacle visual appearance
  void _setObstacleAppearance() {
    switch (obstacleType) {
      case ObstacleType.barrier:
        paint = Paint()..color = Colors.red;
        break;
      case ObstacleType.train:
        paint = Paint()..color = Colors.grey[800]!;
        break;
      case ObstacleType.sign:
        paint = Paint()..color = Colors.yellow[700]!;
        break;
      case ObstacleType.box:
        paint = Paint()..color = Colors.brown;
        break;
    }
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    
    if (!isActive) return;
    
    // Move obstacle down
    position.y += moveSpeed * dt;
    
    // Remove if off screen
    if (position.y > GameConstants.gameHeight + size.y) {
      _removeObstacle();
    }
  }
  
  /// Remove obstacle from game
  void _removeObstacle() {
    isActive = false;
    removeFromParent();
  }
  
  /// Handle collision with player
  @override
  bool onCollision(Set<Vector2> intersectionPoints, PositionComponent other) {
    super.onCollision(intersectionPoints, other);
    
    if (other.runtimeType.toString() == 'Player' && isActive) {
      // Notify game of collision
      _handlePlayerCollision();
    }
    
    return true;
  }
  
  /// Handle player collision
  void _handlePlayerCollision() {
    // This will be handled by the collision detector in the game
    print('Player hit obstacle!');
  }
  
  /// Get collision rectangle
  Rect get collisionRect {
    return Rect.fromLTWH(
      position.x,
      position.y,
      size.x,
      size.y,
    );
  }
  
  /// Check if obstacle is visible on screen
  bool get isOnScreen {
    return position.y < GameConstants.gameHeight && 
           position.y + size.y > 0;
  }
  
  /// Get obstacle center position
  Vector2 get centerPosition => position + size / 2;
  
  /// Update movement speed (for difficulty scaling)
  void updateSpeed(double newSpeed) {
    moveSpeed = newSpeed;
  }
}

/// Types of obstacles
enum ObstacleType {
  barrier,
  train,
  sign,
  box,
}

/// Extension for obstacle type properties
extension ObstacleTypeExtension on ObstacleType {
  String get name {
    switch (this) {
      case ObstacleType.barrier:
        return 'Barrier';
      case ObstacleType.train:
        return 'Train';
      case ObstacleType.sign:
        return 'Sign';
      case ObstacleType.box:
        return 'Box';
    }
  }
  
  Color get color {
    switch (this) {
      case ObstacleType.barrier:
        return Colors.red;
      case ObstacleType.train:
        return Colors.grey[800]!;
      case ObstacleType.sign:
        return Colors.yellow[700]!;
      case ObstacleType.box:
        return Colors.brown;
    }
  }
  
  double get damageValue {
    switch (this) {
      case ObstacleType.barrier:
        return 1.0;
      case ObstacleType.train:
        return 2.0;
      case ObstacleType.sign:
        return 0.5;
      case ObstacleType.box:
        return 1.0;
    }
  }
  
  /// Get random obstacle type
  static ObstacleType getRandom() {
    final types = ObstacleType.values;
    return types[GameHelpers.randomInt(0, types.length - 1)];
  }
}
