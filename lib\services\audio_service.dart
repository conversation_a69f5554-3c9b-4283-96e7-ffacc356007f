import 'package:audioplayers/audioplayers.dart';
import 'package:flame_audio/flame_audio.dart';
import '../utils/constants.dart';
import 'storage_service.dart';

/// Audio service for managing game sounds and music
class AudioService {
  static AudioService? _instance;
  late AudioPlayer _musicPlayer;
  late AudioPlayer _sfxPlayer;
  
  bool _soundEnabled = true;
  bool _musicEnabled = true;
  bool _isMusicPlaying = false;
  
  AudioService._() {
    _musicPlayer = AudioPlayer();
    _sfxPlayer = AudioPlayer();
    _loadSettings();
  }
  
  /// Singleton instance
  static AudioService get instance {
    _instance ??= AudioService._();
    return _instance!;
  }
  
  /// Initialize audio service
  Future<void> initialize() async {
    await _loadSettings();
    await _preloadSounds();
  }
  
  /// Load audio settings from storage
  Future<void> _loadSettings() async {
    final storage = StorageService.instance;
    _soundEnabled = storage.getSoundEnabled();
    _musicEnabled = storage.getMusicEnabled();
  }
  
  /// Preload sound effects for better performance
  Future<void> _preloadSounds() async {
    try {
      await FlameAudio.audioCache.loadAll([
        'jump.wav',
        'coin.wav',
        'crash.wav',
        'powerup.wav',
        'button_click.wav',
        'whoosh.wav',
      ]);
    } catch (e) {
      print('Error preloading sounds: $e');
    }
  }
  
  /// Play background music
  Future<void> playBackgroundMusic() async {
    if (!_musicEnabled || _isMusicPlaying) return;
    
    try {
      await _musicPlayer.play(AssetSource('audio/background_music.mp3'));
      await _musicPlayer.setReleaseMode(ReleaseMode.loop);
      await _musicPlayer.setVolume(0.6);
      _isMusicPlaying = true;
    } catch (e) {
      print('Error playing background music: $e');
    }
  }
  
  /// Stop background music
  Future<void> stopBackgroundMusic() async {
    try {
      await _musicPlayer.stop();
      _isMusicPlaying = false;
    } catch (e) {
      print('Error stopping background music: $e');
    }
  }
  
  /// Pause background music
  Future<void> pauseBackgroundMusic() async {
    try {
      await _musicPlayer.pause();
    } catch (e) {
      print('Error pausing background music: $e');
    }
  }
  
  /// Resume background music
  Future<void> resumeBackgroundMusic() async {
    if (!_musicEnabled) return;
    
    try {
      await _musicPlayer.resume();
    } catch (e) {
      print('Error resuming background music: $e');
    }
  }
  
  /// Play jump sound
  Future<void> playJumpSound() async {
    if (!_soundEnabled) return;
    await _playSoundEffect('jump.wav');
  }
  
  /// Play coin collection sound
  Future<void> playCoinSound() async {
    if (!_soundEnabled) return;
    await _playSoundEffect('coin.wav');
  }
  
  /// Play crash sound
  Future<void> playCrashSound() async {
    if (!_soundEnabled) return;
    await _playSoundEffect('crash.wav', volume: 0.8);
  }
  
  /// Play power-up sound
  Future<void> playPowerUpSound() async {
    if (!_soundEnabled) return;
    await _playSoundEffect('powerup.wav');
  }
  
  /// Play button click sound
  Future<void> playButtonClickSound() async {
    if (!_soundEnabled) return;
    await _playSoundEffect('button_click.wav', volume: 0.5);
  }
  
  /// Play whoosh sound (for movement)
  Future<void> playWhooshSound() async {
    if (!_soundEnabled) return;
    await _playSoundEffect('whoosh.wav', volume: 0.4);
  }
  
  /// Play generic sound effect
  Future<void> _playSoundEffect(String fileName, {double volume = 0.7}) async {
    try {
      await FlameAudio.play(fileName, volume: volume);
    } catch (e) {
      print('Error playing sound effect $fileName: $e');
    }
  }
  
  /// Enable/disable sound effects
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await StorageService.instance.saveSoundEnabled(enabled);
  }
  
  /// Enable/disable background music
  Future<void> setMusicEnabled(bool enabled) async {
    _musicEnabled = enabled;
    await StorageService.instance.saveMusicEnabled(enabled);
    
    if (!enabled && _isMusicPlaying) {
      await stopBackgroundMusic();
    } else if (enabled && !_isMusicPlaying) {
      await playBackgroundMusic();
    }
  }
  
  /// Get sound enabled status
  bool get soundEnabled => _soundEnabled;
  
  /// Get music enabled status
  bool get musicEnabled => _musicEnabled;
  
  /// Get music playing status
  bool get isMusicPlaying => _isMusicPlaying;
  
  /// Set master volume for music
  Future<void> setMusicVolume(double volume) async {
    try {
      await _musicPlayer.setVolume(volume.clamp(0.0, 1.0));
    } catch (e) {
      print('Error setting music volume: $e');
    }
  }
  
  /// Fade in background music
  Future<void> fadeInMusic({Duration duration = const Duration(seconds: 2)}) async {
    if (!_musicEnabled) return;
    
    try {
      await _musicPlayer.setVolume(0.0);
      await playBackgroundMusic();
      
      const steps = 20;
      const stepDuration = Duration(milliseconds: 100);
      final volumeStep = 0.6 / steps;
      
      for (int i = 0; i <= steps; i++) {
        await _musicPlayer.setVolume(i * volumeStep);
        await Future.delayed(stepDuration);
      }
    } catch (e) {
      print('Error fading in music: $e');
    }
  }
  
  /// Fade out background music
  Future<void> fadeOutMusic({Duration duration = const Duration(seconds: 2)}) async {
    try {
      const steps = 20;
      const stepDuration = Duration(milliseconds: 100);
      final currentVolume = 0.6;
      final volumeStep = currentVolume / steps;
      
      for (int i = steps; i >= 0; i--) {
        await _musicPlayer.setVolume(i * volumeStep);
        await Future.delayed(stepDuration);
      }
      
      await stopBackgroundMusic();
    } catch (e) {
      print('Error fading out music: $e');
    }
  }
  
  /// Dispose audio resources
  Future<void> dispose() async {
    try {
      await _musicPlayer.dispose();
      await _sfxPlayer.dispose();
    } catch (e) {
      print('Error disposing audio service: $e');
    }
  }
  
  /// Play sound with pitch variation (for variety)
  Future<void> playSoundWithPitch(String fileName, {double pitch = 1.0}) async {
    if (!_soundEnabled) return;
    
    try {
      // Note: Pitch control might not be available in all platforms
      // This is a placeholder for future implementation
      await _playSoundEffect(fileName);
    } catch (e) {
      print('Error playing sound with pitch: $e');
    }
  }
  
  /// Play random sound from a list
  Future<void> playRandomSound(List<String> soundFiles) async {
    if (!_soundEnabled || soundFiles.isEmpty) return;
    
    final randomIndex = (soundFiles.length * (DateTime.now().millisecondsSinceEpoch % 1000) / 1000).floor();
    await _playSoundEffect(soundFiles[randomIndex]);
  }
}
