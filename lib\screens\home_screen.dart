import 'package:flutter/material.dart';

import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../services/storage_service.dart';
import '../services/audio_service.dart';
import '../models/player_data.dart';
import 'game_screen.dart';
import 'shop_screen.dart';
import 'achievements_screen.dart';
import 'statistics_screen.dart';
import 'settings_screen.dart';

/// Home screen for Metro Dash
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  PlayerData? playerData;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadPlayerData();
    _initializeAudio();
  }

  /// Setup animations
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: GameConstants.longAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutBack,
          ),
        );

    _animationController.forward();
  }

  /// Load player data
  Future<void> _loadPlayerData() async {
    try {
      final data = await StorageService.instance.loadPlayerData();
      setState(() {
        playerData = data;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        playerData = PlayerData();
        isLoading = false;
      });
    }
  }

  /// Initialize audio
  Future<void> _initializeAudio() async {
    await AudioService.instance.initialize();
    if (playerData?.musicEnabled ?? true) {
      AudioService.instance.playBackgroundMusic();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        backgroundColor: GameConstants.backgroundColor,
        body: Center(
          child: CircularProgressIndicator(color: GameConstants.primaryColor),
        ),
      );
    }

    return Scaffold(
      backgroundColor: GameConstants.backgroundColor,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF1A1A2E), Color(0xFF16213E), Color(0xFF0F3460)],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(child: _buildMainContent()),
                  _buildFooter(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build header with title and stats
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          // Game title
          Text(
            GameConstants.gameTitle,
            style: const TextStyle(
              fontSize: 48,
              fontWeight: FontWeight.bold,
              color: GameConstants.textColor,
              shadows: [
                Shadow(
                  offset: Offset(2, 2),
                  blurRadius: 4,
                  color: Colors.black54,
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),

          // Subtitle
          Text(
            'Endless Runner Adventure',
            style: TextStyle(
              fontSize: 16,
              color: GameConstants.textColor.withValues(alpha: 0.8),
              fontStyle: FontStyle.italic,
            ),
          ),

          const SizedBox(height: 20),

          // Stats row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatCard(
                'High Score',
                GameHelpers.formatScore(playerData?.highScore ?? 0),
                Icons.star,
                GameConstants.secondaryColor,
              ),
              _buildStatCard(
                'Coins',
                '${playerData?.totalCoins ?? 0}',
                Icons.monetization_on,
                GameConstants.coinColor,
              ),
              _buildStatCard(
                'Games',
                '${playerData?.gamesPlayed ?? 0}',
                Icons.play_arrow,
                GameConstants.primaryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build stat card
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: GameConstants.textColor.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Build main content with buttons
  Widget _buildMainContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 40.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Play button
          _buildMainButton(
            'PLAY',
            Icons.play_arrow,
            GameConstants.primaryColor,
            _startGame,
          ),

          const SizedBox(height: 20),

          // Secondary buttons row
          Row(
            children: [
              Expanded(
                child: _buildSecondaryButton(
                  'Shop',
                  Icons.shopping_cart,
                  GameConstants.secondaryColor,
                  _openShop,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: _buildSecondaryButton(
                  'Settings',
                  Icons.settings,
                  Colors.grey[600]!,
                  _openSettings,
                ),
              ),
            ],
          ),

          const SizedBox(height: 15),

          // Additional buttons row
          Row(
            children: [
              Expanded(
                child: _buildSecondaryButton(
                  'Achievements',
                  Icons.emoji_events,
                  Colors.amber,
                  _openAchievements,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: _buildSecondaryButton(
                  'Statistics',
                  Icons.bar_chart,
                  Colors.green,
                  _openStatistics,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build main play button
  Widget _buildMainButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      width: double.infinity,
      height: 70,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color, color.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(35),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(35),
          onTap: () {
            AudioService.instance.playButtonClickSound();
            GameHelpers.lightVibrate();
            onPressed();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.white, size: 32),
              const SizedBox(width: 12),
              Text(
                text,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build secondary button
  Widget _buildSecondaryButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: color.withValues(alpha: 0.5), width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(25),
          onTap: () {
            AudioService.instance.playButtonClickSound();
            GameHelpers.lightVibrate();
            onPressed();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                text,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build footer
  Widget _buildFooter() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Audio controls
          Row(
            children: [
              _buildAudioButton(
                playerData?.soundEnabled ?? true
                    ? Icons.volume_up
                    : Icons.volume_off,
                _toggleSound,
              ),
              const SizedBox(width: 10),
              _buildAudioButton(
                playerData?.musicEnabled ?? true
                    ? Icons.music_note
                    : Icons.music_off,
                _toggleMusic,
              ),
            ],
          ),

          // Version info
          Text(
            'v1.0.0',
            style: TextStyle(
              fontSize: 12,
              color: GameConstants.textColor.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  /// Build audio control button
  Widget _buildAudioButton(IconData icon, VoidCallback onPressed) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: IconButton(
        icon: Icon(icon, color: GameConstants.textColor, size: 20),
        onPressed: onPressed,
      ),
    );
  }

  /// Start game
  void _startGame() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GameScreen(playerData: playerData!),
      ),
    );
  }

  /// Open shop
  void _openShop() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ShopScreen(playerData: playerData!),
      ),
    );
  }

  /// Open settings
  void _openSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SettingsScreen(playerData: playerData!),
      ),
    );
  }

  /// Open achievements
  void _openAchievements() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AchievementsScreen(playerData: playerData!),
      ),
    );
  }

  /// Open statistics
  void _openStatistics() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StatisticsScreen(playerData: playerData!),
      ),
    );
  }

  /// Toggle sound
  void _toggleSound() async {
    final newValue = !(playerData?.soundEnabled ?? true);
    await AudioService.instance.setSoundEnabled(newValue);
    setState(() {
      playerData = playerData?.copyWith(soundEnabled: newValue);
    });
  }

  /// Toggle music
  void _toggleMusic() async {
    final newValue = !(playerData?.musicEnabled ?? true);
    await AudioService.instance.setMusicEnabled(newValue);
    setState(() {
      playerData = playerData?.copyWith(musicEnabled: newValue);
    });
  }
}
