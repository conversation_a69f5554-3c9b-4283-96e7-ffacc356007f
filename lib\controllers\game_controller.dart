import 'package:flutter/material.dart';
import 'package:flame/components.dart';
import '../game.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../services/storage_service.dart';
import '../models/player_data.dart';

/// Game controller for managing game state and logic
class GameController {
  final MetroDashGame game;

  // Game state
  String currentState = GameConstants.gameStateMenu;
  DateTime? gameStartTime;
  Duration gameDuration = Duration.zero;

  // Statistics
  int currentScore = 0;
  int currentCoins = 0;
  double currentDistance = 0.0;
  int currentMultiplier = 1;

  // Power-up states
  bool magnetActive = false;
  bool shieldActive = false;
  bool multiplierActive = false;
  bool jetpackActive = false;

  // Timers for power-ups
  DateTime? magnetEndTime;
  DateTime? shieldEndTime;
  DateTime? multiplierEndTime;
  DateTime? jetpackEndTime;

  // Player data
  late PlayerData playerData;

  GameController(this.game);

  /// Initialize the game controller
  Future<void> initialize() async {
    await _loadPlayerData();
    print('Game controller initialized');
  }

  /// Load player data from storage
  Future<void> _loadPlayerData() async {
    try {
      playerData = await StorageService.instance.loadPlayerData();
    } catch (e) {
      print('Error loading player data: $e');
      playerData = PlayerData();
    }
  }

  /// Start a new game
  Future<void> startGame() async {
    currentState = GameConstants.gameStatePlaying;
    gameStartTime = DateTime.now();

    // Reset game statistics
    currentScore = 0;
    currentCoins = 0;
    currentDistance = 0.0;
    currentMultiplier = 1;

    // Reset power-up states
    _resetPowerUps();

    // Start the game
    game.startGame();

    print('Game started by controller');
  }

  /// Pause the game
  void pauseGame() {
    if (currentState == GameConstants.gameStatePlaying) {
      currentState = GameConstants.gameStatePaused;
      game.pauseGame();
      print('Game paused');
    }
  }

  /// Resume the game
  void resumeGame() {
    if (currentState == GameConstants.gameStatePaused) {
      currentState = GameConstants.gameStatePlaying;
      game.resumeGame();
      print('Game resumed');
    }
  }

  /// End the game
  Future<void> endGame() async {
    currentState = GameConstants.gameStateGameOver;

    // Calculate final statistics
    if (gameStartTime != null) {
      gameDuration = DateTime.now().difference(gameStartTime!);
    }

    // Update player data
    await _updatePlayerData();

    // Save player data
    await _savePlayerData();

    game.endGame();

    print('Game ended. Final score: $currentScore');
  }

  /// Update game statistics
  void updateGameStats({
    int? score,
    int? coins,
    double? distance,
    int? multiplier,
  }) {
    if (score != null) currentScore = score;
    if (coins != null) currentCoins = coins;
    if (distance != null) currentDistance = distance;
    if (multiplier != null) currentMultiplier = multiplier;
  }

  /// Activate power-up
  void activatePowerUp(PowerUpType type) {
    final now = DateTime.now();

    switch (type) {
      case PowerUpType.magnet:
        magnetActive = true;
        magnetEndTime = now.add(
          Duration(seconds: GameConstants.magnetDuration.toInt()),
        );
        break;

      case PowerUpType.shield:
        shieldActive = true;
        shieldEndTime = now.add(
          Duration(seconds: GameConstants.shieldDuration.toInt()),
        );
        game.player.activateShield(GameConstants.shieldDuration);
        break;

      case PowerUpType.multiplier:
        multiplierActive = true;
        multiplierEndTime = now.add(
          Duration(seconds: GameConstants.multiplierDuration.toInt()),
        );
        currentMultiplier = 2;
        break;

      case PowerUpType.jetpack:
        jetpackActive = true;
        jetpackEndTime = now.add(const Duration(seconds: 3));
        break;
    }

    print('Power-up activated: ${type.name}');
  }

  /// Update power-up states
  void updatePowerUps() {
    final now = DateTime.now();

    // Check magnet
    if (magnetActive && magnetEndTime != null && now.isAfter(magnetEndTime!)) {
      magnetActive = false;
      magnetEndTime = null;
    }

    // Check shield
    if (shieldActive && shieldEndTime != null && now.isAfter(shieldEndTime!)) {
      shieldActive = false;
      shieldEndTime = null;
    }

    // Check multiplier
    if (multiplierActive &&
        multiplierEndTime != null &&
        now.isAfter(multiplierEndTime!)) {
      multiplierActive = false;
      multiplierEndTime = null;
      currentMultiplier = 1;
    }

    // Check jetpack
    if (jetpackActive &&
        jetpackEndTime != null &&
        now.isAfter(jetpackEndTime!)) {
      jetpackActive = false;
      jetpackEndTime = null;
    }
  }

  /// Reset all power-ups
  void _resetPowerUps() {
    magnetActive = false;
    shieldActive = false;
    multiplierActive = false;
    jetpackActive = false;

    magnetEndTime = null;
    shieldEndTime = null;
    multiplierEndTime = null;
    jetpackEndTime = null;
  }

  /// Check if player can take damage (considering shield)
  bool canTakeDamage() {
    return !shieldActive && !game.player.isShielded;
  }

  /// Handle coin collection with magnet effect
  void handleCoinCollection(Vector2 coinPosition) {
    if (magnetActive) {
      // Attract coins within range
      final playerPosition = game.player.centerPosition;
      final distance = (coinPosition - playerPosition).length;

      if (distance < 100) {
        // Magnet range
        // Move coin towards player
        // This would be implemented in the coin component
      }
    }
  }

  /// Get remaining time for power-up
  Duration? getPowerUpRemainingTime(PowerUpType type) {
    final now = DateTime.now();

    switch (type) {
      case PowerUpType.magnet:
        return magnetEndTime?.difference(now);
      case PowerUpType.shield:
        return shieldEndTime?.difference(now);
      case PowerUpType.multiplier:
        return multiplierEndTime?.difference(now);
      case PowerUpType.jetpack:
        return jetpackEndTime?.difference(now);
    }
  }

  /// Update player data after game
  Future<void> _updatePlayerData() async {
    // Update high score
    final newHighScore = playerData.updateHighScore(currentScore);

    // Add coins
    playerData.addCoins(currentCoins);

    // Record game completion
    playerData.recordGameCompletion(
      currentScore,
      currentDistance,
      gameDuration,
    );

    if (newHighScore) {
      print('New high score: $currentScore');
    }
  }

  /// Save player data to storage
  Future<void> _savePlayerData() async {
    try {
      await StorageService.instance.savePlayerData(playerData);
      print('Player data saved successfully');
    } catch (e) {
      print('Error saving player data: $e');
    }
  }

  /// Get current difficulty level
  DifficultyLevel getCurrentDifficulty() {
    return GameHelpers.getDifficultyLevel(currentDistance);
  }

  /// Get formatted game time
  String getFormattedGameTime() {
    return GameHelpers.formatTime(gameDuration);
  }

  /// Get formatted score
  String getFormattedScore() {
    return GameHelpers.formatScore(currentScore);
  }

  /// Get formatted distance
  String getFormattedDistance() {
    return '${(currentDistance / 10).floor()}m';
  }

  /// Check if new achievement unlocked
  List<String> checkNewAchievements() {
    final previousData = PlayerData.fromJson(playerData.toJson());

    // This would compare current achievements with previous ones
    // and return newly unlocked achievements
    return [];
  }

  /// Get power-up status for UI
  Map<PowerUpType, bool> getPowerUpStatus() {
    return {
      PowerUpType.magnet: magnetActive,
      PowerUpType.shield: shieldActive,
      PowerUpType.multiplier: multiplierActive,
      PowerUpType.jetpack: jetpackActive,
    };
  }

  /// Get game statistics for UI
  Map<String, dynamic> getGameStats() {
    return {
      'score': currentScore,
      'coins': currentCoins,
      'distance': currentDistance,
      'multiplier': currentMultiplier,
      'time': gameDuration,
      'difficulty': getCurrentDifficulty(),
    };
  }
}
